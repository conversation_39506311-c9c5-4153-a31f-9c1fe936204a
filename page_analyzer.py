#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面分析工具
详细分析路由器主页面内容
"""

import requests
import re
import json
from urllib.parse import urljoin

class PageAnalyzer:
    def __init__(self, target_ip="************"):
        self.target_ip = target_ip
        self.base_url = f"http://{target_ip}"
        self.session = requests.Session()
        
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        })
    
    def analyze_main_page(self):
        """分析主页面"""
        print("=== 主页面详细分析 ===")
        
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code != 200:
                print(f"❌ 无法访问主页面，状态码: {response.status_code}")
                return
            
            html = response.text
            print(f"✓ 主页面内容 (长度: {len(html)} 字符):")
            print("=" * 50)
            print(html)
            print("=" * 50)
            
            # 保存页面内容
            with open("main_page.html", "w", encoding="utf-8") as f:
                f.write(html)
            print("✓ 主页面已保存为 main_page.html")
            
            # 分析页面结构
            self.analyze_page_structure(html)
            
            # 查找所有链接
            self.find_all_links(html)
            
            # 查找JavaScript代码
            self.find_javascript(html)
            
        except Exception as e:
            print(f"❌ 主页面分析失败: {e}")
    
    def analyze_page_structure(self, html):
        """分析页面结构"""
        print("\n=== 页面结构分析 ===")
        
        # 查找标题
        title_match = re.search(r'<title>(.*?)</title>', html, re.IGNORECASE)
        if title_match:
            print(f"页面标题: {title_match.group(1)}")
        
        # 查找meta信息
        meta_pattern = r'<meta[^>]*>'
        meta_matches = re.findall(meta_pattern, html, re.IGNORECASE)
        if meta_matches:
            print(f"Meta标签: {len(meta_matches)} 个")
            for meta in meta_matches[:5]:  # 只显示前5个
                print(f"  {meta}")
        
        # 查找表单
        form_pattern = r'<form[^>]*>(.*?)</form>'
        form_matches = re.findall(form_pattern, html, re.DOTALL | re.IGNORECASE)
        if form_matches:
            print(f"表单: {len(form_matches)} 个")
            for i, form in enumerate(form_matches):
                print(f"  表单 {i+1}: {form[:100]}...")
        
        # 查找iframe
        iframe_pattern = r'<iframe[^>]*>'
        iframe_matches = re.findall(iframe_pattern, html, re.IGNORECASE)
        if iframe_matches:
            print(f"IFrame: {len(iframe_matches)} 个")
            for iframe in iframe_matches:
                print(f"  {iframe}")
    
    def find_all_links(self, html):
        """查找所有链接"""
        print("\n=== 链接分析 ===")
        
        # 查找所有href链接
        href_pattern = r'href=["\']([^"\']+)["\']'
        href_matches = re.findall(href_pattern, html, re.IGNORECASE)
        
        # 查找所有src链接
        src_pattern = r'src=["\']([^"\']+)["\']'
        src_matches = re.findall(src_pattern, html, re.IGNORECASE)
        
        # 查找所有action链接
        action_pattern = r'action=["\']([^"\']+)["\']'
        action_matches = re.findall(action_pattern, html, re.IGNORECASE)
        
        all_links = set(href_matches + src_matches + action_matches)
        
        if all_links:
            print(f"发现 {len(all_links)} 个链接:")
            for link in sorted(all_links):
                print(f"  {link}")
                
                # 尝试访问每个链接
                self.test_link_access(link)
        else:
            print("未发现任何链接")
    
    def test_link_access(self, link):
        """测试链接访问"""
        try:
            # 构建完整URL
            if link.startswith('http'):
                full_url = link
            elif link.startswith('/'):
                full_url = self.base_url + link
            else:
                full_url = self.base_url + '/' + link
            
            # 跳过一些不需要测试的链接
            skip_extensions = ['.css', '.js', '.png', '.jpg', '.gif', '.ico']
            if any(link.lower().endswith(ext) for ext in skip_extensions):
                return
            
            response = self.session.get(full_url, timeout=5)
            
            if response.status_code == 200:
                print(f"    ✓ 可访问 (长度: {len(response.text)})")
                
                # 如果响应很长，可能包含有用信息
                if len(response.text) > 1000:
                    print(f"    可能包含配置信息，已保存")
                    filename = link.replace('/', '_').replace('\\', '_').replace('?', '_')
                    with open(f"page_{filename}.html", "w", encoding="utf-8") as f:
                        f.write(response.text)
                        
            elif response.status_code == 401:
                print(f"    ⚠ 需要认证")
            elif response.status_code == 403:
                print(f"    ⚠ 访问被禁止")
            elif response.status_code == 404:
                print(f"    ❌ 页面不存在")
            else:
                print(f"    状态码: {response.status_code}")
                
        except Exception as e:
            pass  # 忽略连接错误
    
    def find_javascript(self, html):
        """查找JavaScript代码"""
        print("\n=== JavaScript分析 ===")
        
        # 查找内联JavaScript
        script_pattern = r'<script[^>]*>(.*?)</script>'
        script_matches = re.findall(script_pattern, html, re.DOTALL | re.IGNORECASE)
        
        if script_matches:
            print(f"发现 {len(script_matches)} 个JavaScript代码块:")
            for i, script in enumerate(script_matches):
                if script.strip():
                    print(f"  脚本 {i+1} (长度: {len(script)} 字符):")
                    print(f"    {script[:200]}...")
                    
                    # 保存脚本内容
                    with open(f"inline_script_{i+1}.js", "w", encoding="utf-8") as f:
                        f.write(script)
                    
                    # 分析脚本内容
                    self.analyze_script_content(script, f"inline_script_{i+1}")
        
        # 查找外部JavaScript文件
        js_src_pattern = r'<script[^>]*src=["\']([^"\']+)["\']'
        js_src_matches = re.findall(js_src_pattern, html, re.IGNORECASE)
        
        if js_src_matches:
            print(f"发现 {len(js_src_matches)} 个外部JavaScript文件:")
            for js_file in js_src_matches:
                print(f"  {js_file}")
                self.download_js_file(js_file)
    
    def analyze_script_content(self, script, script_name):
        """分析脚本内容"""
        # 查找敏感信息
        sensitive_patterns = [
            r'password\s*[:=]\s*["\']([^"\']+)["\']',
            r'admin\s*[:=]\s*["\']([^"\']+)["\']',
            r'ssid\s*[:=]\s*["\']([^"\']+)["\']',
            r'key\s*[:=]\s*["\']([^"\']+)["\']',
            r'192\.168\.\d+\.\d+',
            r'http://[^"\']+',
            r'function\s+(\w*login\w*)',
            r'function\s+(\w*auth\w*)'
        ]
        
        found_info = []
        for pattern in sensitive_patterns:
            matches = re.findall(pattern, script, re.IGNORECASE)
            if matches:
                found_info.extend(matches)
        
        if found_info:
            print(f"    {script_name} 中发现敏感信息:")
            for info in found_info:
                print(f"      {info}")
    
    def download_js_file(self, js_file):
        """下载JavaScript文件"""
        try:
            if js_file.startswith('http'):
                js_url = js_file
            elif js_file.startswith('/'):
                js_url = self.base_url + js_file
            else:
                js_url = self.base_url + '/' + js_file
            
            response = self.session.get(js_url, timeout=10)
            if response.status_code == 200:
                filename = js_file.replace('/', '_').replace('\\', '_')
                with open(f"external_{filename}", "w", encoding="utf-8") as f:
                    f.write(response.text)
                print(f"    ✓ 已下载: external_{filename}")
                
                # 分析下载的JavaScript
                self.analyze_script_content(response.text, filename)
                
        except Exception as e:
            print(f"    ❌ 下载失败: {e}")
    
    def check_common_vulnerabilities(self):
        """检查常见漏洞"""
        print("\n=== 常见漏洞检查 ===")
        
        # 检查目录遍历
        traversal_paths = [
            "/../../../etc/passwd",
            "/cgi-bin/../../../etc/passwd",
            "/../../../windows/system32/drivers/etc/hosts"
        ]
        
        for path in traversal_paths:
            try:
                response = self.session.get(self.base_url + path, timeout=5)
                if response.status_code == 200 and ("root:" in response.text or "localhost" in response.text):
                    print(f"✓ 目录遍历漏洞: {path}")
                    with open("traversal_result.txt", "w") as f:
                        f.write(response.text)
            except:
                continue
        
        # 检查默认文件
        default_files = [
            "/robots.txt",
            "/sitemap.xml",
            "/.htaccess",
            "/web.config",
            "/crossdomain.xml"
        ]
        
        for file_path in default_files:
            try:
                response = self.session.get(self.base_url + file_path, timeout=5)
                if response.status_code == 200:
                    print(f"✓ 发现默认文件: {file_path}")
                    filename = file_path.replace('/', '_').replace('.', '_')
                    with open(f"default{filename}.txt", "w") as f:
                        f.write(response.text)
            except:
                continue
    
    def run_analysis(self):
        """运行完整分析"""
        print("=== 页面分析工具 ===")
        print(f"目标: {self.target_ip}")
        
        # 1. 分析主页面
        self.analyze_main_page()
        
        # 2. 检查常见漏洞
        self.check_common_vulnerabilities()
        
        print("\n=== 分析完成 ===")
        print("所有发现的文件已保存到当前目录")

if __name__ == "__main__":
    analyzer = PageAnalyzer()
    analyzer.run_analysis()
