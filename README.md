# 水星FW325R路由器配置获取工具

本工具集提供多种技术方法来获取水星FW325R路由器的配置信息，适用于已连接WiFi但不知道管理密码的情况。

## ⚠️ 重要声明
- 本工具仅用于合法的网络管理和安全测试
- 请确保您有权限访问目标路由器
- 不得用于非法入侵他人网络设备

## 工具说明

### 1. router_scanner.py - 基础扫描工具
主要功能：
- 自动检测路由器IP地址
- 尝试常见的默认登录凭据
- 扫描路由器管理页面
- 提取WiFi配置信息

### 2. advanced_router_tools.py - 高级分析工具
主要功能：
- 端口扫描
- 漏洞检查
- CSRF绕过尝试
- 配置文件分析
- SNMP扫描

## 使用方法

### 方法1：运行基础扫描
```bash
python router_scanner.py
```

### 方法2：运行高级扫描
```bash
python advanced_router_tools.py
```

## 手动尝试方法

### 1. 默认凭据尝试
在浏览器中访问 `http://***********` 或您的路由器IP，尝试以下凭据：

| 用户名 | 密码 |
|--------|------|
| admin | admin |
| admin | (空) |
| admin | 123456 |
| admin | password |
| root | admin |

### 2. 浏览器开发者工具方法
1. 打开路由器登录页面
2. 按F12打开开发者工具
3. 查看网络请求，寻找配置文件或API调用
4. 检查本地存储和Cookie

### 3. 直接访问配置页面
尝试直接访问以下URL：
- `http://***********/backup.bin`
- `http://***********/config.xml`
- `http://***********/userRpm/StatusRpm.htm`
- `http://***********/goform/wirelessBasic`

### 4. 路由器复位按钮方法（最后手段）
如果其他方法都失败：
1. 找到路由器背面的Reset按钮
2. 在路由器通电状态下，用牙签按住Reset按钮10秒
3. 路由器将恢复出厂设置，可使用默认凭据登录

## 水星FW325R特定信息

### 常见默认设置
- 管理IP: ***********
- 默认用户名: admin
- 默认密码: admin 或 空
- Web管理端口: 80

### 已知漏洞和绕过方法
1. **配置文件直接访问**: 某些版本允许直接下载配置文件
2. **CSRF漏洞**: 可能存在跨站请求伪造漏洞
3. **弱认证**: 部分页面可能不需要认证即可访问

## 故障排除

### 无法连接到路由器
1. 确认您已连接到路由器的WiFi
2. 检查路由器IP地址（可能不是***********）
3. 尝试ping路由器IP确认连通性

### 脚本运行错误
1. 确保已安装Python 3.x
2. 安装所需依赖：`pip install requests`
3. 检查防火墙设置

### 无法获取配置信息
1. 路由器可能已更新固件，修复了漏洞
2. 尝试不同的URL路径
3. 考虑使用专业的渗透测试工具

## 其他建议

### 网络分析工具
- Wireshark: 抓包分析网络流量
- Nmap: 专业的网络扫描工具
- Burp Suite: Web应用安全测试

### 路由器固件分析
如果可以获取到固件文件：
1. 使用binwalk分析固件结构
2. 提取文件系统
3. 查找配置文件和密码

### 硬件方法
对于高级用户：
1. 串口调试：通过UART接口访问
2. JTAG调试：硬件级别的调试接口
3. 固件dump：直接读取Flash芯片

## 安全建议

获取到路由器访问权限后：
1. 立即更改默认密码
2. 更新路由器固件
3. 禁用不必要的服务
4. 启用防火墙和访问控制

## 法律声明

使用本工具时请遵守当地法律法规，仅在您拥有合法权限的设备上使用。作者不承担因误用本工具造成的任何法律责任。
