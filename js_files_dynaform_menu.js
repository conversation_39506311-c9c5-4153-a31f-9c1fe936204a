var menu={netWorkData_menu:{value:menuStr.netData,className:"hsMenuNetWork",url:"WanCfg.htm",hasSub:!0,subMenu:[{value:menuStr.wanSet,url:"WanCfg.htm"},{value:menuStr.lanSet,url:"LanCfg.htm"},{value:menuStr.macSet,url:"MacClone.htm"},{value:menuStr.DHCPServer,url:"DHCPServer.htm"},{value:menuStr.IPMACBind,url:"IPMACBind.htm"}]},wifiSet_menu:{value:menuStr.wifi,className:"hsMenuWlan",url:"WlanNetwork.htm",hasSub:!0,subMenu:[{value:menuStr.wifiHost,url:"WlanNetwork.htm"},{value:menuStr.wifiGuestNet,
url:"WlanGuestNetWorkCfg.htm"},{value:menuStr.wifiWDS,url:"WlanWDSCfg.htm"}]},netControl_menu:{value:menuStr.netControl,className:"hsMenuNetControl",url:"ParentControl.htm",hasSub:!0,subMenu:[{value:menuStr.parentControl,url:"ParentControl.htm"},{value:menuStr.actionControl,url:"AccessCtrl.htm"}]},highUser_menu:{value:menuStr.highUser,className:"hsMenuAdvUser",url:"VirtualServerCfg.htm",hasSub:!0,subMenu:[{value:menuStr.virtualS,url:"VirtualServerCfg.htm"},{value:menuStr.DMZS,url:"DMZCfg.htm"},{value:menuStr.upnpSet,
url:"UpnpCfg.htm"},{value:menuStr.router,url:"RouteTable.htm"},{value:menuStr.ddns,url:"DdnsCfg.htm"}]},sysTool_menu:{value:menuStr.sysTool,className:"hsMenuEquipment",url:"ManageSettingUp.htm",hasSub:!0,subMenu:[{value:menuStr.routerManage,url:"ManageSettingUp.htm"},{value:menuStr.sntp,url:"DateTimeCfg.htm"},{value:menuStr.upgrade,url:"SysUpgrade.htm"},{value:menuStr.restore,url:"SysReset.htm"},{value:menuStr.backupload,url:"SysBakNRestore.htm"},{value:menuStr.reboot,url:"SysReboot.htm"},{value:menuStr.changeLgPwd,
url:"SysChangeLgPwd.htm"},{value:menuStr.diagnostic,url:"Diagnostic.htm"},{value:menuStr.syslog,url:"SystemLog.htm"}]}},advanceMenuClicked=!1;function pageOnloadMenuCallback(){advanceMenuClicked=!1}
function menuClick(f,d,e){e=$("#highSetMenu li");var a="",c,b;if(!0!=advanceMenuClicked){advanceMenuClicked=!0;for(var g=0,h=e.length;g<h;g++)b=e[g],1==b.nodeType&&(c=b.className,"menuLi"==c||"menuLiClick"==c?b.id==f?(b.className="menuLiClick",a=d):b.className="menuLi":0<=b.id.indexOf(f)?(0==parseInt(b.id.substring(f.length))?b.className="subMenuLiClick":b.className="subMenuLi",b.style.display="inline-block"):b.style.display="none");void 0!=a&&menuLoad(a)}}
function subMenuClick(f,d,e){var a=$("#highSetMenu li");if(!0!=advanceMenuClicked){advanceMenuClicked=!0;for(var c=0,b=a.length;c<b;c++)if("subMenuLi"==a[c].className||"subMenuLiClick"==a[c].className)a[c].id==d?(a[c].className="subMenuLiClick",menuLoad(e)):0<=a[c].id.indexOf(f)&&(a[c].className="subMenuLi")}}function menuLoad(f){var d=id("hcDetail");!0==isIESix&&(d.style.height="");d.innerHTML="";loadPage(f,"hcDetail",function(){highSetAutoFit()});window.scrollTo(0,0)}
function menuInit(){var f=id("highSetMenu"),d,e,a,c,b,g;for(g in menu)if(d=menu[g],e=document.createElement("ul"),e.className="menuUl",f.appendChild(e),a=document.createElement("li"),a.id=g.toString(),a.className="menuLi",a.onclick=new Function("menuClick('"+g.toString()+"', '"+d.url+"', '"+d.className+"')"),c=document.createElement("i"),c.className="menuImg "+d.className,a.appendChild(c),b=document.createElement("label"),b.innerHTML=d.value,b.className="menuLbl",a.appendChild(b),c=document.createElement("i"),
c.className="menuDown",a.appendChild(c),e.appendChild(a),d.hasSub)for(c in d=d.subMenu,d)a=document.createElement("li"),a.id=g.toString()+c,a.className="subMenuLi",0==c&&(a.style.marginTop="5px"),c==d.length-1&&(a.style.marginBottom="12px"),a.style.display="none",a.onclick=new Function("subMenuClick('"+g.toString()+"', '"+a.id+"', '"+d[c].url+"')"),b=document.createElement("i"),b.className="subImg",a.appendChild(b),b=document.createElement("label"),b.innerHTML=d[c].value,b.className="subMenuLbl",
a.appendChild(b),e.appendChild(a);f.childNodes[0].childNodes[0].onclick()};
