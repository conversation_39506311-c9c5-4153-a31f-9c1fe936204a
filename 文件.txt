┌──(kali㉿kali)-[~]
└─$ # 启动Metasploit寻找水星路由器的exploit
msfconsole -q -x "search mercury; search router; search http_login; exit"

Matching Modules
================

   #   Name                                                   Disclosure Date  Rank       Check  Description
   -   ----                                                   ---------------  ----       -----  -----------
   0   auxiliary/server/android_mercury_parseuri              .                normal     No     Android Mercury Browser Intent URI Scheme and Directory Traversal Vulnerability
   1   exploit/windows/misc/hp_loadrunner_magentproc_cmdexec  2010-05-06       excellent  No     HP Mercury LoadRunner Agent magentproc.exe Remote Command Execution
   2   exploit/windows/browser/hpmqc_progcolor                2007-04-04       normal     No     HP Mercury Quality Center ActiveX Control ProgColor Buffer Overflow
   3   exploit/windows/smtp/mercury_cram_md5                  2007-08-18       great      No     Mercury Mail SMTP AUTH CRAM-MD5 Buffer Overflow
   4   exploit/windows/imap/mercury_login                     2007-03-06       normal     Yes    Mercury/32 4.01 IMAP LOGIN SEH Buffer Overflow
   5   exploit/windows/misc/mercury_phonebook                 2005-12-19       average    No     Mercury/32 PH Server Module Buffer Overflow
   6     \_ target: Windows XP Pro SP0/SP1 English            .                .          .      .
   7     \_ target: Windows 2000 Pro English ALL              .                .          .      .
   8   exploit/windows/imap/mercury_rename                    2004-11-29       average    Yes    Mercury/32 v4.01a IMAP RENAME Buffer Overflow
   9     \_ target: Automatic                                 .                .          .      .
   10    \_ target: Windows 2000 SP4 English                  .                .          .      .
   11    \_ target: Windows XP Pro SP0 English                .                .          .      .
   12    \_ target: Windows XP Pro SP1 English                .                .          .      .


Interact with a module by name or index. For example info 12, use 12 or use exploit/windows/imap/mercury_rename
After interacting with a module you can manually set a TARGET with set TARGET 'Windows XP Pro SP1 English'


Matching Modules
================

   #    Name                                                                     Disclosure Date  Rank       Check  Description
   -    ----                                                                     ---------------  ----       -----  -----------
   0    auxiliary/admin/2wire/xslt_password_reset                                2007-08-15       normal     No     2Wire Cross-Site Request Forgery Password Reset Vulnerability
   1    exploit/linux/misc/asus_infosvr_auth_bypass_exec                         2015-01-04       excellent  No     ASUS infosvr Auth Bypass Command Execution
   2    exploit/linux/http/airties_login_cgi_bof                                 2015-03-31       normal     Yes    Airties login-cgi Buffer Overflow
   3    auxiliary/gather/tplink_archer_c7_traversal                              2015-04-08       normal     Yes    Archer C7 Directory Traversal Vulnerability
   4    auxiliary/admin/http/arris_motorola_surfboard_backdoor_xss               2015-04-08       normal     No     Arris / Motorola Surfboard SBG6580 Web Interface Takeover
   5    exploit/windows/misc/avaya_winpmd_unihostrouter                          2011-05-23       normal     No     Avaya WinPMD UniteHostRouter Buffer Overflow
   6      \_ target: Avaya WinPMD 3.8.2 / Windows XP SP3                         .                .          .      .
   7      \_ target: Avaya WinPMD 3.8.2 / Windows 2003 SP2                       .                .          .      .
   8    auxiliary/bnat/bnat_router                                               .                normal     No     BNAT Router
   9    exploit/linux/http/belkin_login_bof                                      2014-05-09       normal     Yes    Belkin Play N750 login.cgi Buffer Overflow
   10   post/linux/busybox/enum_connections                                      .                normal     No     BusyBox Enumerate Connections
   11   post/linux/busybox/jailbreak                                             .                normal     No     BusyBox Jailbreak
   12   post/linux/busybox/ping_net                                              .                normal     No     BusyBox Ping Network Enumeration
   13   exploit/unix/http/ctek_skyrouter                                         2011-09-08       average    No     CTEK SkyRouter 4200 and 4300 Command Execution
   14   auxiliary/scanner/dlsw/dlsw_leak_capture                                 2014-11-17       normal     Yes    Cisco DLSw Information Disclosure Scanner
   15   auxiliary/scanner/http/cisco_device_manager                              2000-10-26       normal     No     Cisco Device HTTP Device Manager Access
   16   auxiliary/dos/cisco/ios_http_percentpercent                              2000-04-26       normal     No     Cisco IOS HTTP GET /%% Request Denial of Service
   17   auxiliary/scanner/http/cisco_ios_auth_bypass                             2001-06-27       normal     No     Cisco IOS HTTP Unauthorized Administrative Access
   18   exploit/linux/http/cisco_rv340_lan                                       2021-11-02       excellent  Yes    Cisco RV Series Authentication Bypass and Command Injection
   19     \_ target: Unix Command                                                .                .          .      .
   20     \_ target: Linux Dropper                                               .                .          .      .
   21   exploit/linux/http/cve_2019_1663_cisco_rmi_rce                           2019-02-27       good       Yes    Cisco RV110W/RV130(W)/RV215W Routers Management Interface Remote Command Execution
   22     \_ target: Cisco RV110W 1.1.0.9                                        .                .          .      .
   23     \_ target: Cisco RV110W 1.2.0.9                                        .                .          .      .
   24     \_ target: Cisco RV110W 1.2.0.10                                       .                .          .      .
   25     \_ target: Cisco RV110W 1.2.1.4                                        .                .          .      .
   26     \_ target: Cisco RV110W 1.2.1.7                                        .                .          .      .
   27     \_ target: Cisco RV130/RV130W < 1.0.3.45                               .                .          .      .
   28     \_ target: Cisco RV215W 1.1.0.5                                        .                .          .      .
   29     \_ target: Cisco RV215W 1.1.0.6                                        .                .          .      .
   30     \_ target: Cisco RV215W 1.2.0.14                                       .                .          .      .
   31     \_ target: Cisco RV215W 1.2.0.15                                       .                .          .      .
   32     \_ target: Cisco RV215W 1.3.0.7                                        .                .          .      .
   33     \_ target: Cisco RV215W 1.3.0.8                                        .                .          .      .
   34   exploit/linux/http/cisco_rv32x_rce                                       2018-09-09       normal     Yes    Cisco RV320 and RV325 Unauthenticated Remote Code Execution
   35   auxiliary/gather/cisco_rv320_config                                      2019-01-24       normal     No     Cisco RV320/RV326 Configuration Disclosure
   36   exploit/linux/misc/cisco_rv340_sslvpn                                    2022-02-02       good       Yes    Cisco RV340 SSL VPN Unauthenticated Remote Code Execution
   37   exploit/linux/http/cisco_rv_series_authbypass_and_rce                    2021-04-07       excellent  Yes    Cisco Small Business RV Series Authentication Bypass and Command Injection
   38     \_ target: Unix Command                                                .                .          .      .
   39     \_ target: Linux Dropper                                               .                .          .      .
   40   exploit/unix/http/pfsense_clickjacking                                   2017-11-21       normal     No     Clickjacking Vulnerability In CSRF Error Page pfSense
   41   auxiliary/admin/http/dlink_dir_300_600_exec_noauth                       2013-02-04       normal     No     D-Link DIR-600 / DIR-300 Unauthenticated Remote Command Execution
   42   exploit/linux/http/dlink_dir605l_captcha_bof                             2012-10-08       manual     Yes    D-Link DIR-605L Captcha Handling Buffer Overflow
   43   exploit/linux/http/dlink_diagnostic_exec_noauth                          2013-03-05       excellent  No     D-Link DIR-645 / DIR-815 diagnostic.php Command Execution
   44     \_ target: CMD                                                         .                .          .      .
   45     \_ target: Linux mipsel Payload                                        .                .          .      .
   46   exploit/linux/upnp/dlink_dir859_subscribe_exec                           2019-12-24       excellent  No     D-Link DIR-859 Unauthenticated Remote Command Execution
   47   exploit/linux/http/dlink_dir615_up_exec                                  2013-02-07       excellent  No     D-Link DIR615h OS Command Injection
   48     \_ target: CMD                                                         .                .          .      .
   49     \_ target: Linux mipsel Payload                                        .                .          .      .
   50   exploit/linux/http/dlink_dsl2750b_exec_noauth                            2016-02-05       great      Yes    D-Link DSL-2750B OS Command Injection
   51     \_ target: Linux mipsbe Payload                                        .                .          .      .
   52     \_ target: Linux mipsel Payload                                        .                .          .      .
   53   exploit/linux/http/dlink_hnap_header_exec_noauth                         2015-02-13       normal     Yes    D-Link Devices HNAP SOAPAction-Header Command Execution
   54     \_ target: MIPS Little Endian                                          .                .          .      .
   55     \_ target: MIPS Big Endian                                             .                .          .      .
   56   exploit/linux/http/dlink_upnp_exec_noauth                                2013-07-05       normal     Yes    D-Link Devices UPnP SOAP Command Execution
   57     \_ target: MIPS Little Endian                                          .                .          .      .
   58     \_ target: MIPS Big Endian                                             .                .          .      .
   59   exploit/linux/http/dlink_command_php_exec_noauth                         2013-02-04       excellent  No     D-Link Devices Unauthenticated Remote Command Execution
   60   exploit/linux/http/dlink_dir300_exec_telnet                              2013-04-22       excellent  No     D-Link Devices Unauthenticated Remote Command Execution
   61   exploit/linux/upnp/dlink_upnp_msearch_exec                               2013-02-01       excellent  Yes    D-Link Unauthenticated Remote Command Execution using UPnP via a special crafted M-SEARCH packet.
   62     \_ target: Unix Command                                                .                .          .      .
   63     \_ target: Linux Dropper                                               .                .          .      .
   64   exploit/linux/http/dlink_authentication_cgi_bof                          2013-02-08       normal     Yes    D-Link authentication.cgi Buffer Overflow
   65   exploit/linux/http/dlink_hedwig_cgi_bof                                  2013-02-08       normal     Yes    D-Link hedwig.cgi Buffer Overflow in Cookie Header
   66   exploit/linux/http/multi_ncc_ping_exec                                   2015-02-26       normal     Yes    D-Link/TRENDnet NCC Service Command Injection
   67     \_ target: Linux mipsel Payload                                        .                .          .      .
   68     \_ target: Linux mipsbe Payload                                        .                .          .      .
   69   exploit/linux/http/dlink_dir850l_unauth_exec                             2017-08-09       excellent  Yes    DIR-850L (Un)authenticated OS Command Exec
   70   exploit/linux/http/dlink_hnap_login_bof                                  2016-11-07       excellent  Yes    Dlink DIR Routers Unauthenticated HNAP Login Stack Buffer Overflow
   71     \_ target: Dlink DIR-818 / 822 / 823 / 850 [MIPS]                      .                .          .      .
   72     \_ target: Dlink DIR-868 (rev. B and C) / 880 / 885 / 890 / 895 [ARM]  .                .          .      .
   73   auxiliary/gather/huawei_wifi_info                                        2013-11-11       normal     No     Huawei Datacard Information Disclosure Vulnerability
   74   exploit/linux/http/huawei_hg532n_cmdinject                               2017-04-15       excellent  Yes    Huawei HG532n Command Injection
   75   exploit/windows/lotus/domino_icalendar_organizer                         2010-09-14       normal     Yes    IBM Lotus Domino iCalendar MAILTO Buffer Overflow
   76     \_ target: Lotus Domino 8.5 on Windows 2000 SP4                        .                .          .      .
   77     \_ target: Lotus Domino 8.5 on Windows Server 2003 SP0                 .                .          .      .
   78     \_ target: Lotus Domino 8.5 on Windows Server 2003 SP2                 .                .          .      .
   79   auxiliary/scanner/discovery/ipv6_neighbor_router_advertisement           .                normal     No     IPv6 Local Neighbor Discovery Using Router Advertisement
   80   auxiliary/dos/tcp/junos_tcp_opt                                          .                normal     No     Juniper JunOS Malformed TCP Option
   81   exploit/linux/http/linksys_wrt110_cmd_exec                               2013-07-12       excellent  Yes    Linksys Devices pingstr Remote Command Injection
   82   exploit/linux/http/linksys_themoon_exec                                  2014-02-13       excellent  Yes    Linksys E-Series TheMoon Remote Command Injection
   83     \_ target: Linux mipsel Payload                                        .                .          .      .
   84     \_ target: Linux mipsbe Payload                                        .                .          .      .
   85   auxiliary/scanner/http/linksys_e1500_traversal                           .                normal     No     Linksys E1500 Directory Traversal Vulnerability
   86   auxiliary/admin/http/linksys_e1500_e2500_exec                            2013-02-05       normal     No     Linksys E1500/E2500 Remote Command Execution
   87   exploit/linux/http/linksys_e1500_apply_exec                              2013-02-05       excellent  No     Linksys E1500/E2500 apply.cgi Remote Command Injection
   88     \_ target: CMD                                                         .                .          .      .
   89     \_ target: Linux mipsel Payload                                        .                .          .      .
   90   auxiliary/admin/http/linksys_tmunblock_admin_reset_bof                   2014-02-19       normal     No     Linksys WRT120N tmUnblock Stack Buffer Overflow
   91   exploit/linux/http/linksys_wrt160nv2_apply_exec                          2013-02-11       excellent  No     Linksys WRT160nv2 apply.cgi Remote Command Injection
   92     \_ target: CMD                                                         .                .          .      .
   93     \_ target: Linux mipsel Payload                                        .                .          .      .
   94   exploit/linux/http/linksys_apply_cgi                                     2005-09-13       great      No     Linksys WRT54 Access Point apply.cgi Buffer Overflow
   95     \_ target: Generic                                                     .                .          .      .
   96     \_ target: Version 1.42.2                                              .                .          .      .
   97     \_ target: Version 2.02.6beta1                                         .                .          .      .
   98     \_ target: Version 2.02.7_ETSI                                         .                .          .      .
   99     \_ target: Version 3.03.6                                              .                .          .      .
   100    \_ target: Version 4.00.7                                              .                .          .      .
   101    \_ target: Version 4.20.06                                             .                .          .      .
   102  auxiliary/admin/http/linksys_wrt54gl_exec                                2013-01-18       normal     No     Linksys WRT54GL Remote Command Execution
   103  exploit/linux/http/linksys_wrt54gl_apply_exec                            2013-01-18       manual     No     Linksys WRT54GL apply.cgi Command Execution
   104    \_ target: CMD                                                         .                .          .      .
   105    \_ target: Linux mipsel Payload                                        .                .          .      .
   106  exploit/multi/http/lcms_php_exec                                         2011-03-03       excellent  Yes    LotusCMS 3.0 eval() Remote Command Execution
   107  auxiliary/admin/networking/mikrotik_config                               .                normal     No     Mikrotik Configuration Importer
   108    \_ action: ROUTEROS                                                    .                .          .      Import RouterOS Config File
   109    \_ action: SWOS                                                        .                .          .      Import SwOS Config File
   110  post/networking/gather/enum_mikrotik                                     .                normal     No     Mikrotik Gather Device General Information
   111  auxiliary/gather/mikrotik_winbox_fileread                                2018-08-02       normal     No     Mikrotik Winbox Arbitrary File Read
   112  auxiliary/admin/motorola/wr850g_cred                                     2004-09-24       normal     No     Motorola WR850G v4.03 Credentials
   113  auxiliary/gather/netgear_password_disclosure                             .                normal     Yes    NETGEAR Administrator Password Disclosure
   114  exploit/linux/http/netgear_wnr2000_rce                                   2016-12-20       excellent  Yes    NETGEAR WNR2000v5 (Un)authenticated hidden_lang_avi Stack Buffer Overflow
   115  auxiliary/admin/http/netgear_wnr2000_pass_recovery                       2016-12-20       normal     No     NETGEAR WNR2000v5 Administrator Password Recovery
   116  auxiliary/spoof/dns/native_spoofer                                       .                normal     No     Native DNS Spoofer (Example)
   117  exploit/linux/misc/netcore_udp_53413_backdoor                            2014-08-25       normal     Yes    Netcore Router Udp 53413 Backdoor
   118    \_ target: MIPS Little Endian                                          .                .          .      .
   119    \_ target: MIPS Big Endian                                             .                .          .      .
   120  exploit/linux/http/netgear_dgn1000b_setup_exec                           2013-02-06       excellent  No     Netgear DGN1000B setup.cgi Remote Command Execution
   121    \_ target: CMD                                                         .                .          .      .
   122    \_ target: Linux mipsbe Payload                                        .                .          .      .
   123  exploit/linux/http/netgear_dnslookup_cmd_exec                            2017-02-25       excellent  Yes    Netgear DGN2200 dnslookup.cgi Command Injection
   124  exploit/linux/http/netgear_dgn2200b_pppoe_exec                           2013-02-15       manual     No     Netgear DGN2200B pppoe.cgi Remote Command Execution
   125    \_ target: CMD                                                         .                .          .      .
   126    \_ target: Linux mipsbe Payload                                        .                .          .      .
   127  auxiliary/admin/http/netgear_pnpx_getsharefolderlist_auth_bypass         2021-09-06       normal     Yes    Netgear PNPX_GetShareFolderList Authentication Bypass
   128  auxiliary/admin/http/netgear_r6700_pass_reset                            2020-06-15       normal     Yes    Netgear R6700v3 Unauthenticated LAN Admin Password Reset
   129  exploit/linux/http/netgear_r7000_cgibin_exec                             2016-12-06       excellent  Yes    Netgear R7000 and R6400 cgi-bin Command Injection
   130  auxiliary/admin/http/netgear_r7000_backup_cgi_heap_overflow_rce          2021-04-21       normal     Yes    Netgear R7000 backup.cgi Heap Overflow RCE
   131  exploit/linux/http/netis_unauth_rce_cve_2024_48456_and_48457             2024-12-27       excellent  Yes    Netis Router Exploit Chain Reactor (CVE-2024-48455, CVE-2024-48456 and CVE-2024-48457).
   132  exploit/linux/http/netis_unauth_rce_cve_2024_22729                       2024-01-11       excellent  Yes    Netis router MW5360 unauthenticated RCE.
   133  auxiliary/scanner/http/opnsense_login                                    .                normal     No     OPNSense Login Scanner
   134  auxiliary/scanner/wproxy/att_open_proxy                                  2017-08-31       normal     No     Open WAN-to-LAN proxy on AT&T routers
   135    \_ AKA: SharknAT&To                                                    .                .          .      .
   136    \_ AKA: sharknatto                                                     .                .          .      .
   137  auxiliary/gather/peplink_bauth_sqli                                      .                normal     Yes    Peplink Balance routers SQLi
   138  exploit/unix/http/raspap_rce                                             2023-07-31       excellent  Yes    RaspAP Unauthenticated Command Injection
   139    \_ target: Unix Command                                                .                .          .      .
   140    \_ target: Linux Dropper                                               .                .          .      .
   141  exploit/linux/http/realtek_miniigd_upnp_exec_noauth                      2015-04-24       normal     Yes    Realtek SDK Miniigd UPnP SOAP Command Execution
   142    \_ target: MIPS Little Endian                                          .                .          .      .
   143    \_ target: MIPS Big Endian                                             .                .          .      .
   144  auxiliary/scanner/sap/sap_router_info_request                            .                normal     No     SAPRouter Admin Request
   145  auxiliary/scanner/sap/sap_router_portscanner                             .                normal     No     SAPRouter Port Scanner
   146  auxiliary/spoof/cisco/cdp                                                .                normal     No     Send Cisco Discovery Protocol (CDP) Packets
   147  auxiliary/admin/misc/sercomm_dump_config                                 2013-12-31       normal     No     SerComm Device Configuration Dump
   148  exploit/linux/misc/sercomm_exec                                          2013-12-31       great      Yes    SerComm Device Remote Code Execution
   149    \_ target: Generic Linux MIPS Big Endian                               .                .          .      .
   150    \_ target: Generic Linux MIPS Little Endian                            .                .          .      .
   151    \_ target: Manual Linux MIPS Big Endian                                .                .          .      .
   152    \_ target: Manual Linux MIPS Little Endian                             .                .          .      .
   153    \_ target: Cisco WAP4410N                                              .                .          .      .
   154    \_ target: Honeywell WAP-PL2 IP Camera                                 .                .          .      .
   155    \_ target: Netgear DG834                                               .                .          .      .
   156    \_ target: Netgear DG834G                                              .                .          .      .
   157    \_ target: Netgear DG834PN                                             .                .          .      .
   158    \_ target: Netgear DGN1000                                             .                .          .      .
   159    \_ target: Netgear DSG835                                              .                .          .      .
   160    \_ target: Netgear WPNT834                                             .                .          .      .
   161  exploit/linux/http/totolink_unauth_rce_cve_2023_30013                    2023-05-05       excellent  Yes    TOTOLINK Wireless Routers unauthenticated remote command execution vulnerability.
   162    \_ target: Unix Command                                                .                .          .      .
   163    \_ target: Linux Dropper                                               .                .          .      .
   164  exploit/linux/misc/tplink_archer_a7_c7_lan_rce                           2020-03-25       excellent  Yes    TP-Link Archer A7/C7 Unauthenticated LAN Remote Code Execution
   165  exploit/linux/http/trueonline_billion_5200w_rce                          2016-12-26       excellent  No     TrueOnline / Billion 5200W-T Router Unauthenticated Command Injection
   166  exploit/linux/http/trueonline_p660hn_v1_rce                              2016-12-26       excellent  Yes    TrueOnline / ZyXEL P660HN-T v1 Router Unauthenticated Command Injection
   167  exploit/linux/http/trueonline_p660hn_v2_rce                              2016-12-26       excellent  Yes    TrueOnline / ZyXEL P660HN-T v2 Router Authenticated Command Injection
   168  auxiliary/admin/http/zyxel_admin_password_extractor                      .                normal     No     ZyXEL GS1510-16 Password Extractor
   169  exploit/linux/misc/zyxel_multiple_devices_zhttp_lan_rce                  2022-02-01       good       Yes    Zyxel Unauthenticated LAN Remote Code Execution
   170  exploit/linux/http/zyxel_lfi_unauth_ssh_rce                              2022-02-01       excellent  Yes    Zyxel chained RCE using LFI and weak password derivation algorithm
   171    \_ target: Unix Command                                                .                .          .      .
   172    \_ target: Linux Dropper                                               .                .          .      .
   173    \_ target: Interactive SSH                                             .                .          .      .
   174  auxiliary/scanner/http/pfsense_login                                     .                normal     No     pfSense Login Scanner


Interact with a module by name or index. For example info 174, use 174 or use auxiliary/scanner/http/pfsense_login


Matching Modules
================

   #  Name                                                     Disclosure Date  Rank    Check  Description
   -  ----                                                     ---------------  ----    -----  -----------
   0  auxiliary/scanner/http/dlink_dir_300_615_http_login      .                normal  No     D-Link DIR-300A / DIR-320 / DIR-615D HTTP Login Utility
   1  auxiliary/scanner/http/dlink_dir_session_cgi_http_login  .                normal  No     D-Link DIR-300B / DIR-600B / DIR-815 / DIR-645 HTTP Login Utility
   2  auxiliary/scanner/http/dlink_dir_615h_http_login         .                normal  No     D-Link DIR-615H HTTP Login Utility
   3  auxiliary/scanner/http/http_login                        .                normal  No     HTTP Login Utility
   4  auxiliary/scanner/vmware/vmware_http_login               .                normal  No     VMWare Web Login Scanner


Interact with a module by name or index. For example info 4, use 4 or use auxiliary/scanner/vmware/vmware_http_login

                                                                                                                                                                                                                                                 
┌──(kali㉿kali)-[~]
└─$ 
@