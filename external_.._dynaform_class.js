function NiceScroll(b){this.taId=b;this.ta=id(this.taId);if(1!=this.ta.nodeType)return null;this.st=el("label");this.sb=el("div");this.sbcH=this.avg=this.stH=this.scH=this.sbH=0;this.n=20;this.enabled=!0;this.mousePos=null;this.show=this.onSb=this.isScroll=!1;this.wtId=this.checkTt=null;this.woSpeed=this.wSpeed=this.wtCounter=0;this.sbStyle={position:"absolute",zIndex:1001,width:"0.583em"};this.stStyle={width:"0.583em",display:"inline-block",background:"#9FE3D1",borderRadius:"3px",position:"relative",
cursor:"pointer"};this.stOpacity=0.1;"function"!=typeof this.init&&(NiceScroll.prototype.init=function(){var a=this;this.sb.id=this.taId+"niceScrollSb"+(new Date).getTime();setStyle(this.sb,{backgroundColor:this.sbStyle.background||"transparent"});this.sb.appendChild(this.st);document.body.appendChild(this.sb);setStyle(this.sb,this.sbStyle);setStyle(this.st,this.stStyle);this._reset();!1==OS.portable?setStyle(this.ta,{overflow:"hidden"}):setStyle(this.ta,{overflow:"scroll"});this._shSb();this._bind();
window.setTimeout(function(){a.checkTt=window.setTimeout(arguments.callee,10);a._check()},100)},NiceScroll.prototype.scrollBarSet=function(a){if("object"==typeof a)for(var b in a)this.sbStyle[b]=a[b]},NiceScroll.prototype.scrollTipSet=function(a){if("object"==typeof a)for(var b in a)this.stStyle[b]=a[b]},NiceScroll.prototype.scrollTipOpacity=function(a){this.stOpacity=a},NiceScroll.prototype.scrollTo=function(a){a=parseInt(a);if(!0==isNaN(a))return!1;this.ta.scrollTop=a},NiceScroll.prototype._reset=
function(){var a=$(this.ta).offset(),b=this.ta.offsetWidth,d=this.ta.offsetHeight,e=this.ta.scrollHeight,f=parseFloat(getNodeDefaultView(this.ta,"borderTopWidth"))||0,g=parseFloat(getNodeDefaultView(this.ta,"borderBottomWidth"))||0,h=parseFloat(getNodeDefaultView(this.ta,"borderRightWidth"))||0;parseFloat(getNodeDefaultView(this.ta,"borderLeftWidth"));this.scH=e-d+f+g;this.stH=parseInt(0.7*(d/e)*d);this.sbcH=d-(this.stH+2);this.avg=this.scH/this.sbcH;0>=e-d?(setStyle(this.sb,{visibility:"hidden"}),
this.show=!1):(this.show=!0,setStyle(this.sb,{visibility:"visible"}),setStyle(this.sb,{top:a.top+f+"px",height:d+"px",left:a.left+b-h-parseInt(this.sb.offsetWidth)+"px"}),setStyle(this.st,{top:this.ta.scrollTop/this.scH*this.sbcH+"px",height:this.stH+"px"}),!1==this.isScroll&&$("#"+this.sb.id).fadeTo(1E3,this.stOpacity))},NiceScroll.prototype._bind=function(){function a(a){a=a||window.event;var b=a.touches[0].clientY,c=b-d.mousePos.y,c=parseFloat(d.st.style.top)-c,c=c>=d.sbcH?d.sbcH:0>=c?0:c;d.st.style.top=
c+"px";d.mousePos.y=b;d.isScroll=!0;!1==OS.portable&&(d.ta.scrollTop=d.scH*(c/d.sbcH),eventPreventDefault(a));clearSelection(a)}function b(e){detachEvnt(document,"touchmove",a);detachEvnt(document,"touchend",b);!1==d.onSb&&(d.isScroll=!1)}var d=this;document.attachEvent?(this.ta.attachEvent("onmousewheel",function(a){a=a||window.event;d._scroll(a)}),this.sb.attachEvent("onmousewheel",function(a){a=a||window.event;d._scroll(a)})):(this.ta.addEventListener("mousewheel",function(a){a=a||window.event;
d._scroll(a)},!1),this.ta.addEventListener("DOMMouseScroll",function(a){a=a||window.event;d._scroll(a)},!1),this.sb.addEventListener("mousewheel",function(a){a=a||window.event;d._scroll(a)},!1),this.sb.addEventListener("DOMMouseScroll",function(a){a=a||window.event;d._scroll(a)},!1));attachEvnt(this.ta,"touchstart",function(e){e=e||window.event;d.mousePos={x:e.touches[0].clientX,y:e.touches[0].clientY};attachEvnt(document,"touchmove",a);attachEvnt(document,"touchend",b)});this.st.onmousedown=function(a){d.mousePos=
getMousePos(a);document.onmouseup=function(a){document.onmousemove=null;document.onmouseup=null;!1==d.onSb&&(d.isScroll=!1)};document.onmousemove=function(a){var b=getMousePos(a),c=b.y-d.mousePos.y,c=parseFloat(d.st.style.top)+c,c=c>=d.sbcH?d.sbcH:0>=c?0:c;d.st.style.top=c+"px";d.mousePos.y=b.y;d.ta.scrollTop=d.scH*(c/d.sbcH);d.isScroll=!0;clearSelection(a)}};$("#"+this.sb.id)[0].onmouseover=function(a){a=a||window.event;d.onSb=!0;!0==d.show&&d._scrollShow(a)};$("#"+this.sb.id)[0].onmouseout=function(){d.onSb=
!1;d.isScroll=!1}},NiceScroll.prototype._close=function(){this.sb.style.visibility="hidden";this.enabled=!1},NiceScroll.prototype._open=function(){this.enabled=!0},NiceScroll.prototype._shSb=function(){this.sb.style.visibility="none"==this.ta.style.display||"hidden"==this.ta.visibility?"hidden":"visible"},NiceScroll.prototype._check=function(){null==id(this.taId)?(window.clearTimeout(this.checkTt),this.sb.parentNode.removeChild(this.sb)):!1!=this.enabled&&(!1==checkInHorize(this.ta)?this.sb.style.display=
"none":(this.sb.style.display="block",0>=parseInt(this.ta.offsetHeight)&&(this.sb.style.visibility="hidden"),this._reset()))},NiceScroll.prototype._getWheelDelta=function(a){a=a||window.event;return a.wheelDelta?window.opera&&9.5>window.opera.version?-a.wheelDelta:a.wheelDelta:40*-a.detail},NiceScroll.prototype._wheelAnimate=function(a,b){var d=this,e=!1;this.wtId?(this.wtCounter=(e=0>(this.woSpeed^a))?b:50>this.wtCounter+b?this.wtCounter+b:50,this.wSpeed=e?a:1.05*this.wSpeed):(this.wtCounter=b,this.woSpeed=
this.wSpeed=a,function(){var a=0;d.wtId=window.setTimeout(arguments.callee,5);if(0>d.wtCounter)clearTimeout(d.wtId),d.wtId=null,!1==d.onSb&&(d.isScroll=!1);else{a=parseFloat(d.ta.scrollTop)+parseInt(d.wSpeed);if(a>=d.scH||0>=a)d.wtCounter=0;d.ta.scrollTop=a;a=d.ta.scrollTop/d.scH*d.sbcH;isNaN(a)||(d.st.style.top=a+"px");d.wtCounter--}}())},NiceScroll.prototype._scrollShow=function(a){$("#"+this.sb.id).stop(!0).css("visibility","visible").css("opacity",1);this.isScroll=!0;eventPreventDefault(a)},NiceScroll.prototype._scroll=
function(a){a=a||window.event;var b=0<this._getWheelDelta(a)?-1:1;!0==this.show&&!0==this.enabled&&(this._scrollShow(a),this._wheelAnimate(5*b,7))})}
function DateControl(b,a){this.table;this.weekList;this.hourList;this.dateCon=id(b);this.weekIsMouseDown=!1;this.selDate=[0,0,0,0,0,0,0];this.dateArray=[0,0,0,0,0,0,0];this.cellWidth=this.cellHeight=22;this.cellSeColor="#93D24D";this.cellDeColor="#FAFAFA";this.cellPadding=1;void 0==DateControl.prototype.init&&(DateControl.prototype.hourStr=label.hour,DateControl.prototype.weekDayNum=7,DateControl.prototype.lineStr="-",DateControl.prototype.selTag="selTag",DateControl.prototype.cellBorderWidth=1,DateControl.prototype.iCellIndex=
0,DateControl.prototype.weekArray=[label.Mon,label.Tue,label.Wen,label.Thu,label.Fri,label.Sta,label.Sun],DateControl.prototype._init=function(){this._initOptions();this._dateConInit();this._hourListInit();this._weekListInit();this._dateTableInit()},DateControl.prototype.reset=function(a){var b,e,f,g=this.iCellIndex;if(!1!=a instanceof Array&&void 0!=a&&a.length==this.weekDayNum)for(var h=0;h<this.weekDayNum;h++){e=this.table.rows[h];b=a[h];for(var k=0;24>k;k++)f=e.cells[k],f=f.childNodes[g],void 0!=
b?(this._setSel(f,b%2),b>>=1):this._setSel(f,0)}},DateControl.prototype._initOptions=function(){for(var b in a)"undefined"!=typeof this[b]&&(this[b]=a[b])},DateControl.prototype.getSelDate=function(){var a,b,e=this.weekDayNum,f=this.table.rows;this.selDate=[0,0,0,0,0,0,0];for(var g=0;g<e;g++){b=f[g];for(var h=0;24>h;h++)a=b.cells[h],a=a.childNodes[0],a=parseInt(a.getAttribute("sel")),1==a&&(this.selDate[g]+=Math.pow(2,h))}return this.selDate},DateControl.prototype._dateConInit=function(){this.dateCon.style.overflow=
"hidden"},DateControl.prototype._hourListInit=function(){var a=document.createElement("ul"),b,e,f=this;a.className="hourList";for(var g=0;24>=g;g++)b=document.createElement("li"),24!=g?(e=document.createElement("i"),e.innerHTML=g,b.appendChild(e),b.style.width=this.cellWidth+2*this.cellPadding+this.cellBorderWidth+"px",e.onclick=function(a){return function(){for(var b=f.table.rows,d=b.length,c=f.iCellIndex,e=1,g=0;g<d&&(e&=parseInt(b[g].cells[a].childNodes[c].getAttribute("sel")),0!=e);g++);e=1-e;
for(g=0;g<d;g++)f._setSel(b[g].cells[a].childNodes[c],e);clearSelection()}}(g)):b.innerHTML="<i>"+this.hourStr+"</i>",a.appendChild(b);this.dateCon.appendChild(a);this.hourList=a},DateControl.prototype._weekListInit=function(){var a=document.createElement("ul"),b,e=this;a.className="weekList";for(var f=0,g=this.weekDayNum;f<g;f++)b=document.createElement("li"),b.style.height=this.cellHeight+2*this.cellPadding+this.cellBorderWidth+"px",b.style.lineHeight=this.cellHeight+2*this.cellPadding+this.cellBorderWidth+
"px",b.innerHTML=this.weekArray[f],b.onclick=function(a){return function(){for(var b=e.table.rows[a].cells,d=e.iCellIndex,c=1,f=0,g=b.length;f<g&&(c&=parseInt(b[f].childNodes[d].getAttribute("sel")),0!=c);f++);c=1-c;f=0;for(g=b.length;f<g;f++)e._setSel(b[f].childNodes[d],c);clearSelection()}}(f),a.appendChild(b);this.dateCon.appendChild(a);this.weekList=a},DateControl.prototype._setSel=function(a,b){a.setAttribute("sel",b);a.style.backgroundColor=1==b?this.cellSeColor:this.cellDeColor},DateControl.prototype._dateCellCreate=
function(){for(var a,b,e,f,g=this,h=0,k=this.weekDayNum;h<k;h++){b=this.table.insertRow(-1);void 0!=this.dateArray&&(a=this.dateArray[h]);for(var l=0;24>l;l++)e=b.insertCell(-1),e.style.padding=this.cellPadding+"px",e.className="weekTd",f=document.createElement("i"),f.className="tableICell",f.style.height=this.cellHeight+"px",f.style.width=this.cellWidth+"px",e.appendChild(f),this._setSel(f,0),void 0!=a?(this._setSel(f,a%2),a>>=1):this._setSel(f,0),f.onmouseover=function(a){!0==g.weekIsMouseDown&&
g._setSel(this,1-parseInt(this.getAttribute("sel")))},f.onmousedown=function(a){g._setSel(this,1-parseInt(this.getAttribute("sel")))}}3==this.table.rows[0].cells[0].nodeType&&(this.iCellIndex=1)},DateControl.prototype._dateCellBind=function(){var a=this;this.table.onmousedown=function(b){a.weekIsMouseDown=!0;document.onmouseup=function(b){a.weekIsMouseDown=!1}};this.table.onmouseup=function(b){a.weekIsMouseDown=!1}},DateControl.prototype._dateTableCreate=function(){this.table=document.createElement("table");
this.table.className="tableWeek";this.table.cellspacing="0px";this.table.cellpadding="0px";this.dateCon.appendChild(this.table)},DateControl.prototype._dateTableInit=function(){this._dateTableCreate();this._dateCellCreate();this._dateCellBind()});this._init()}
function PageFunc(){this.pathStr="../";this.htmlPathStr=this.pathStr+"common/";this.detectPathStr="/images/logo_fa.png";this.loginId="Login";this.loadPageData={url:"",id:""};this.isPostfunc;this.helpIdStr="helpStr";this.LGKEYSTR="lgKey";this.LGKEYTIMESTR="lgKeyTime";this.$Init=function(){Quary.call(jQuery);$.setDataParseFunc($.parser);$.getExplorer();$.initUrl()};this.logout=function(){$.logout();loadPage("Content.htm","Con");id("Con").innerHTML=""};this.loadPageHandleBg=function(){var b=$("i.helpBtn"),
a;initHoverBd();for(var c=0,d=b.length;c<d;c++)a=b[c],a=a.getAttribute(helpIdStr),null!=a&&helpBind(b[c],a)};this.loadPage=function(b,a,c){var d=this;window.setTimeout(function(){helpClose();closeAlert();closeConfirm();$.load(d.htmlPathStr+b,function(){try{"function"==typeof pageOnloadMenuCallback&&pageOnloadMenuCallback()}catch(a){}"function"==typeof c&&c()},a)},0)};this.pageRedirect=function(){var b=window.top.location.href;USER_GROUP_REMOTE!=$.group&&!1==/^((http:\/\/)*(\d{1,3}\.){3}\d{1,3})/g.test(b)&&
0>b.indexOf("falogin.cn")&&!1==$.local&&(window.top.location.href=$.httpTag+"falogin.cn")};this.pageOnload=function(){var b=[{tag:"link",url:"../dynaform/menu.css"},{tag:"link",url:"../dynaform/DataGrid.css"}],a=[{tag:"script",url:"../lib/verify.js"},{tag:"script",url:"../dynaform/macFactory.js"},{tag:"script",url:"../dynaform/DataGrid.js"},{tag:"script",url:"../dynaform/menu.js"}];"undefined"==typeof phoneTip&&(phoneTip=0);this.loadExternResource({scripts:[{tag:"script",url:"../lib/jquery-1.10.1.min.js"},
{tag:"script",url:"../lib/model.js"},{tag:"script",url:"../lib/DM.js"},{tag:"script",url:"../language/cn/str.js"},{tag:"script",url:"../language/cn/error.js"},{tag:"script",url:"../lib/Quary.js"},{tag:"script",url:"../lib/ajax.js"}],callBack:function(){$Init();$.setexternJSP(replaceJSP);$.setExternPageHandle(loadPageHandleBg);$.setLoginErrHandle(showLogin);$.setPRHandle(pageRedirect);window.authInfo=[];this.loadExternResource({scripts:a,links:b});titleStr.hardwareinfo=document.title;this.compatibleShow();
this.localSgInit();this.loadPage("Content.htm","Con")}});1!=phoneTip&&(document.oncontextmenu=function(a){return!1});if(isIESix)try{document.execCommand("BackgroundImageCache",!1,!0)}catch(c){}};this.unloadDetail=function(b){if(b=id(b))b.innerHTML=""};this.detailShow=function(b,a){$("#"+b).fadeIn(800,a)};this.detailHide=function(b,a){$("#"+b).fadeOut(800,function(){$("#"+b).css("display","none");window.unloadDetail(a)})};this.selectChange=function(b,a){id(b).value=a.options[a.selectedIndex].text};
this.loginChange=function(b){var a=this.id(this.loginId),c="block",d="none",e,f=document.body.childNodes;!0==b&&(c="none",d="block");for(var g in f)e=f[g],void 0!=e.nodeName&&"DIV"==e.nodeName.toUpperCase()&&e.id!=this.loginId&&this.setStyle(e,{display:c});a.innerHTML="";this.setStyle(a,{display:d});!0==b?$.unChgPwd==authInfo[1]&&1==phoneTip?(id("Con").innerHTML="",this.loadPage("PhoneLoginChgPwd.htm",this.loginId)):$.unChgPwd==authInfo[1]?(id("Con").innerHTML="",this.loadPage("LoginChgPwd.htm",this.loginId)):
1==phoneTip?($.setLgPwd(""),this.loadPage("PhoneLogin.htm",this.loginId)):($.setLgPwd(""),this.loadPage("Login.htm",this.loginId)):$.unChgPwd==authInfo[1]?loadPage("Content.htm","Con"):this.isPostfunc?this.isPostfunc():this.loadPage(this.loadPageData.url,this.loadPageData.id)};this.setLoadPage=function(b,a){this.loadPageData.url=b;this.loadPageData.id=a};this.localSgInit=function(){try{this.sessionLS.init(),!0==isIE&&!1==isIENormal&&function(){sessionLS.setExpire(3E3);window.setTimeout(arguments.callee,
1E3)}()}catch(b){}this.getLgPwd()};this.auth=function(){$.auth($.pwd)};this.getLgPwd=function(){try{$.pwd=sessionLS.getItem(this.LGKEYSTR)}catch(b){}};this.showLogin=function(b){this.isPostfunc=b;this.loginChange(!0)};this.unloadLogin=function(){this.loginChange(!1)};this.iFrmOnload=function(b,a,c){var d;c=ENONE;b=id(b);var e=!1;try{d=b.contentWindow?b.contentWindow.document.body?b.contentWindow.document.body.innerHTML:null:b.contentDocument.document.body?b.contentDocument.document.body.innerHTML:
null,e=/([0-9]+)/g.test(d),c=!1==e?EINVFMT:parseInt(RegExp.$1),c!=ENONE&&closeProgressNum(),a(c)}catch(f){closeProgressNum(),a(EINVFMT)}};this.windowSleep=function(b){var a=new Date;for(b=a.getTime()+b;!(a=new Date,a.getTime()>b););};this.getCurrPcMac=function(){var b,a=$.getPeerMac();if(ENONE!=a.errorno||""==a.data)return"00-00-00-00-00-00";b=a.data.indexOf("\r\n");return a.data.substring(0,b)};this.cloneLocalMac=function(){var b=$.readEx(SYSTEM_DATA_ID),a=this.getCurrPcMac(),c=ENONE;b.mac[1]!=a&&
(b.mac[1]=a,c=$.write($.toText(b),$.block));return c};this.logSave=function(){location.href=$.orgURL($.domainUrl+"syslog.txt?code="+TDDP_INSTRUCT+"&asyn=0&disposition=1");return!0};this.loadExternResource=function(b){var a,c,d,e,f=document.getElementsByTagName("head")[0];a={links:null,scripts:null,callBack:null};for(var g in b)a[g]=b[g];b=a.links;c=a.scripts;d=a.callBack;if(void 0!=b)for(var h in b)a=document.createElement("link"),a.rel="stylesheet",a.href=b[h].url,f.appendChild(a);if(void 0!=c){var k;
a=document.createElement("script");a.type="text/javascript";if(void 0!=d)for(h in e=void 0!=a.readyState,k=function(a){c[a].loadState=!0;for(var b in c)if(!1==c[b].loadState)return;d()},c)c[h].loadState=!1;for(h in c)a=document.createElement("script"),a.type="text/javascript",void 0!=d&&(e?a.onreadystatechange=function(a){return function(){if("loaded"==this.readyState||"complete"==this.readyState)this.onreadystatechange=null,k(a)}}(h):a.onload=function(a){return function(){k(a)}}(h)),a.src=c[h].url,
f.appendChild(a)}}}function Cover(){Style.call(this);this.CoverId="Cover";this.hideCover=function(b,a){var c=id(this.CoverId);this.setStyle(c,{display:"none",visibility:"hidden"});this.setStyle(c,a);"undefined"!=typeof b&&b(c);c.innerHTML=""};this.showCover=function(b,a){var c=id(this.CoverId);this.setStyle(c,{display:"block",visibility:"visible"});this.setStyle(c,a);"undefined"!=typeof b&&b(c)}}
function Style(){this.disableCol="#b2b2b2";this.setStyle=function(b,a){if(null!=b&&null!=a&&1==b.nodeType)for(var c in a)try{b.style[c]=a[c]}catch(d){}};this.getNodeDefaultView=function(b,a){var c=null;if(!b)return null;try{return c=b.currentStyle?b.currentStyle:document.defaultView.getComputedStyle(b,null),void 0!=a?c[a]:c}catch(d){}}}
function LocalStorageSD(){try{this.sessionLS=null==this.sessionStorage?{file_name:"user_data_default_SD",dom:null,init:function(){var a=document.createElement("input");a.type="hidden";a.addBehavior("#default#userData");document.body.appendChild(a);a.save(this.file_name);this.dom=a},setItem:function(a,b){this.dom.setAttribute(a,b);this.dom.save(this.file_name)},getItem:function(a,b){this.dom.load(this.file_name);return this.dom.getAttribute(a)},removeItem:function(a){this.dom.removeAttribute(a);this.dom.save(this.file_name)},
setExpire:function(a){var b=new Date,b=new Date(b.getTime()+a);this.dom.load(this.file_name);this.dom.expires=b.toUTCString();this.dom.save(this.file_name)}}:sessionStorage}catch(b){}}
function Explorer(){this.isIETenLess=this.isIENormal=this.isIESeven=this.isIESix=this.isIE=!1;this.explorerInfo=navigator.userAgent;this.getIEInfo=function(){isIE=/msie ((\d+\.)+\d+)/i.test(explorerInfo)?document.mode||RegExp.$1:!1;!1!=isIE&&(6>=isIE?this.isIESix=!0:7==isIE?this.isIESeven=!0:9<=isIE&&(this.isIENormal=!0),10>=isIE&&(this.isIETenLess=!0),this.isIE=!0)};this.compatibleShow=function(){if(!0==this.isIESix){var b,a,c,d,e;0<=document.cookie.indexOf("ieSixClosed")||(b=$("div.ieSixCompatible"),
void 0==b[0]&&(b=el("div"),b.className="ieSixCompatible",a=el("div"),a.className="ieSixCpCon",c=el("i"),d=el("span"),d.className="spanNote",d.innerHTML=label.IESixCpTip,e=el("span"),e.className="spanClose",e.innerHTML=label.iknown,e.onclick=function(){document.cookie="ieSixClosed=true";b.style.visibility="hidden";b.style.top="-9999px"},a.appendChild(c),a.appendChild(d),a.appendChild(e),b.appendChild(a),document.body.appendChild(b)))}};this.createGroupRadio=function(b){var a;if(void 0==b)return a;
!0==this.isIE&&!1==this.isIENormal?a=document.createElement("<input name='"+b+"' />"):(a=document.createElement("input"),a.name=b);return a};this.getIEInfo()}
function Tool(){Style.call(this);this.id=function(b){if(void 0!=b)return document.getElementById(b)};this.el=function(b){try{return document.createElement(b)}catch(a){return null}};this.replaceJSP=function(b){var a=null,c,d=/<%(\w+)\.(\w+)%>/i,a=d.exec(b);try{for(;null!=a;)c=language[a[1]][a[2]],b=b.replace("<%"+a[1]+"."+a[2]+"%>",c),a=d.exec(b)}catch(e){}return b};this.getoffset=function(b){for(var a={top:0,left:0};a.left+=parseInt(b.offsetLeft),a.top+=parseInt(b.offsetTop),b=b.offsetParent,null!=
b;);return a};this.attachEvnt=function(b,a,c){0==a.indexOf("on")&&(a=a.substring(2));document.body.attachEvent?b.attachEvent("on"+a,c):b.addEventListener(a,c,!1)};this.detachEvnt=function(b,a,c){0==a.indexOf("on")&&(a=a.substring(2));document.body.attachEvent?b.detachEvent("on"+a,c):b.removeEventListener(a,c,!1)};this.stopProp=function(b){b=b||window.event;b.stopPropagation?b.stopPropagation():b.cancelBubble=!0};this.eventPreventDefault=function(b){b=b||window.event;b.preventDefault?b.preventDefault():
b.returnValue=!1};this.clearSelection=function(){window.getSelection?window.getSelection().removeAllRanges():document.selection.empty()};this.setDomCursorPos=function(b,a){if(b.setSelectionRange)b.focus(),b.setSelectionRange(a,a);else if(b.createTextRange){var c=b.createTextRange();c.collapse(!0);c.moveEnd("character",a);c.moveStart("character",a);c.select()}};this.getMousePos=function(b){b=b||window.event;var a=document;return b.pageX||b.pageY?{x:b.pageX,y:b.pageY}:{x:b.clientX+a.documentElement.scrollLeft-
a.documentElement.clientLeft,y:b.clientY+a.documentElement.scrollTop-a.documentElement.clientTop}};this.isArray=function(b){return"[object Array]"===Object.prototype.toString.call(b)};this.upDown=function(b,a,c,d,e){if(void 0!=c&&void 0!=d){var f=this.el("label");f.className=d;f.onclick=function(){$("#"+a).slideToggle("normal",function(){f.className=f.className==c?d:c;if(e)try{e()}catch(a){}})};b.appendChild(f);return f}};this.arrowUpDown=function(b,a,c){this.upDown(b,a,"arrowUp","arrowDown",c)};
this.getChildNode=function(b,a,c){b=b.childNodes;var d=[],e=0,f;f=a.split(" ");a=f[0];for(var g=f[1],h=0,k=b.length;h<k;h++)f=b[h],f.tagName.toLowerCase()==a&&(void 0!=g&&f.type==g?(d[e]=f,e++):void 0==g&&(d[e]=f,e++));return void 0!=c?d[c]:d[0]};this.checkInHorize=function(b){for(;null!=b&&"HTML"!=b.nodeName.toUpperCase();){if("hidden"==this.getNodeDefaultView(b,"visibility")||"none"==this.getNodeDefaultView(b,"display"))return!1;b=b.parentNode}return!0};this.setUrlHash=function(b,a){var c,d,e;c=
"";var f=location.href;d=location.hash;void 0!=b&&void 0!=a&&0!=b.length&&(0!=d.length?(e=d.indexOf(b),0<=e?(c=d.substring(0,e),d=d.substring(e),e=d.indexOf("#"),0<e?(d=d.substring(e),d=c+b+"="+a+d):d=c+b+"="+a):("#"!=d.substring(d.length-1)&&(c="#"),d+=c+b+"="+a),location.href=f.substring(0,f.indexOf("#"))+d):f.lastIndexOf("#")==f.length-1?location.href+=b+"="+a:location.href+="#"+b+"="+a)};this.getUrlHash=function(b){var a=location.hash,c,d="";if(0<a.indexOf(b)){var a=a.substring(1).split("#"),
e;for(e in a)if(c=a[e].split("="),c[0]==b){d=c[1];break}}return d};this.changeUrlHash=function(b){var a=location.href,c=a.indexOf("#");void 0!=b&&(location.href=0<c?a.substring(0,c+1)+b:a+"#"+b)};this.setInputCursor=function(b){this.setDomCursorPos(b,b.value.length)};this.getCNStrLen=function(b){return b.replace(/[^\x00-\xFF]/g,"xxx").length};this.getCNStrHTMLLen=function(b){return b.replace(/[^\x00-\xFF]/g,"xx").length};this.getStrInMax=function(b,a){var c="",d,e=0;d=b.replace(/[A-Z]/g,"xx");if(getCNStrHTMLLen(d)<=
a)return b;for(var f=1;f<=a;f++){d=b.charAt(e);if(""==d)break;2==getCNStrHTMLLen(d)||!0==/[A-Z]/g.test(d)?(f++,c+=d,beCut=!0):c+=d;e++}return c+"..."};this.EncodeURLIMG=document.createElement("img");this.escapeDBC=function(b){var a=this.EncodeURLIMG;if(!b)return"";if(window.ActiveXObject)return execScript('SetLocale "zh-cn"',"vbscript"),b.replace(/[\d\D]/g,function(a){window.vbsval="";execScript('window.vbsval=Hex(Asc("'+a+'"))',"vbscript");return"%"+window.vbsval.slice(0,2)+"%"+window.vbsval.slice(-2)});
a.src="nothing.png?separator="+b;return a.src.split("?separator=").pop()};this.encodeURL=function(b){return encodeURIComponent(b)};this.doNothing=function(){return!0};this.htmlEscape=function(b){void 0!=b&&(b=b.toString().replace(/[<>&"]/g,function(a){switch(a){case "<":return"&lt;";case ">":return"&gt;";case "&":return"&amp;";case '"':return"&quot;"}}));return b};this.simulateMouseC=function(b){simulateMouseC=!0==isIE&&!1==isIENormal?function(a){var b=document.createEventObject();b.sceenX=100;b.sceenY=
0;b.clientX=0;b.clientY=0;b.ctrlKey=!1;b.altKey=!1;b.shiftKey=!1;b.button=0;a.fireEvent("onclick",b)}:function(){var a=document.createEvent("MouseEvents");a.initMouseEvent("click",!0,!0,document.defaultView,0,0,0,0,0,!1,!1,!1,!1,0,null);b.dispatchEvent(a)};simulateMouseC(b)}}
function Switch(b,a,c){this.switchCon=id(b);this.switchBall=$("#"+b+" i.switchBall")[0];this.switchBg=$("#"+b+" i.switchBg")[0];this.callback=c;this.state=a;this.rightPos;"function"!=typeof Switch.prototype.switchInit&&(Switch.prototype.switchInit=function(){var a=this.state,b=this.switchBall,c=this.switchCon,g=this.switchBg;null!=c&&null!=b&&(this.rightPos=c.offsetWidth-b.offsetWidth,this.setState(a),this.callback&&this.callback(a),b.onmousedown=this.draggableBind(),g.onclick=this.switchBgClick())},
Switch.prototype.setState=function(a){var b=this.switchCon,c=this.switchBall,g=this.switchBg;this.state=a;b.value=a;c.style.left=(1-a)*this.rightPos+(1==a?2:-2)+"px";g.style.background=1==a?"url(../images/basic_fa.png) no-repeat scroll -1px -298px":"url(../images/basic_fa.png) no-repeat scroll -49px -298px";hsSwitchState(b.id,a)},Switch.prototype.switchChgState=function(a){a=1-a;this.setState(a);this.callback&&this.callback(a)},Switch.prototype.switchCHandle=function(){var a=this.state,b=this.switchBall,
c=1==a?1:-1,g=parseInt(b.style.left),h=this.rightPos-2,k=this;0==a&&2>=g||1==a&&g>=h?this.switchChgState(a):(b.style.left=g+c*h/8+"px",window.setTimeout(function(){k.switchCHandle()},20))},Switch.prototype.msMove=function(a,b,c){b=b.x-c.x;c=this.switchCon.offsetWidth-a.offsetWidth-2;b=2<b?b:2;a.style.left=(b>c?c:b)+"px"},Switch.prototype.switchBgClick=function(){var a=this;return function(b){b=b||window.event;a.switchBg==(b.target||b.srcElement)&&a.switchCHandle()}},Switch.prototype.draggableBind=
function(){var a=this;return function(b){b=b?b:window.event;var c=getMousePos(b),g=b.target||b.srcElement,h={x:c.x-g.offsetLeft};document.onmousemove=function(b){b=b?b:window.event;b=getMousePos(b);clearSelection();a.msMove(g,b,h)};document.onmouseup=function(b){clearSelection();document.onmousemove=null;document.onmouseup=null;a.switchCHandle()};stopProp(b)}});this.switchInit()}
function HighSet(){this.hsLoadingObj={subBtnId:"",isBasic:!1,isWl:!1,handleCor:"",handleLoad:"",subLoadingHeight:""};this.hsStatSet=function(b,a,c){var d,e;void 0==c?(d=$("div.handleRelCon")[0],e=$("div.handleRelCon i.state")[0],c=$("div.handleRelCon span.state")[0]):(d=$("#"+c)[0],e=$("#"+c+" i.state")[0],c=$("#"+c+" span.state")[0]);switch(b){case "null":d.style.visibility="hidden";break;case "correct":e.style.background="url(../images/wzd_fa.png) no-repeat -277px -128px";c.style.color="#808080";
c.innerHTML=a;d.style.visibility="visible";break;case "error":e.style.background="url(../images/wzd_fa.png) no-repeat -277px -108px";c.style.color="#FF6060";c.innerHTML=a;d.style.visibility="visible";break;case "link":e.style.background="url(../images/wzd_fa.png) no-repeat -301px -148px",c.style.color="#FFC600",c.innerHTML=a,d.style.visibility="visible"}};this.disInputTip=function(b,a){null!=b&&void 0!=a&&("error"==a.toLowerCase()?this.setStyle(b,{visibility:"visible",background:"url(../images/wzd_fa.png) no-repeat -277px -108px"}):
"ok"==a.toLowerCase()?this.setStyle(b,{visibility:"visible",background:"url(../images/wzd_fa.png) no-repeat -277px -128px"}):"warn"==a.toLowerCase()?this.setStyle(b,{visibility:"visible",background:"url(../images/wzd_fa.png) no-repeat -277px -148px"}):b.style.visibility="hidden")};this.initHoverBd=function(){$("input.hoverBd").bind("focus",function(){this.parentNode.style.border="1px solid #A0D468"}).bind("blur",function(){this.parentNode.style.border="1px solid #FFFFFF"})};this.disableInput=function(b,
a){var c="object"==typeof b?b:id(b);c.disabled=a?!0:!1;c.style.color=a?"#B2B2B2":"#000000";c.style.backgroundColor="#FFFFFF"};this.disableBtn=function(b,a,c,d,e){b="object"==typeof b?b:id(b);for(var f=b.className.split(" "),g=0,h=f.length;g<h;g++)if(f[g]==c)f[g]=d;else if(f[g]==d)return;b.className=f.join(" ");b.onclick=a?null:e};this.setLoadingId=function(b,a,c){this.hsLoadingObj.subBtnId=b;this.hsLoadingObj.isBasic=void 0!=a;this.hsLoadingObj.isWl=void 0!=c};this.hsLoading=function(b){var a=this.hsLoadingObj,
c=id(a.subBtnId),d,e,f,g,h,k,l=this;null!=c&&(h=c.parentNode,h.style.position="relative",c.className=c.className,d=c.offsetLeft,f=c.offsetTop,g=c.offsetWidth,e=c.offsetHeight,k=$("#"+a.subBtnId+" ~ i.hsSubLoading")[0],void 0==k&&(k=el("i"),k.className="hsSubLoading",h.appendChild(k),a.subLoadingHeight=parseInt(getNodeDefaultView(k,"height"))),b?(window.clearTimeout(a.handleLoad),window.clearTimeout(a.handleCor),"none"!=k.style.display&&(k.style.display="none"),2<=c.childNodes.length&&(b=$("#"+a.subBtnId+
" i")[0],g=b.offsetWidth,e=b.offsetHeight),k.style.background="url(../images/wanDetecting_fa.gif)",k.style.left=d+g+5+"px",k.style.top=f+(e-a.subLoadingHeight)/2+"px",k.style.display="inline-block"):(l.hsLoadingObj.subBtnId="",ENONE!=$.result.errorno?void 0!=k&&(k.style.display="none"):a.handleLoad=$.setTimeout(function(){void 0!=k&&(k.style.background="url(../images/wzd_fa.png) no-repeat -277px -128px",l.hsLoadingObj.handleCor=$.setTimeout(function(){void 0!=k&&(k.style.display="none")},2E3))},1E3)))};
this.hsSwitchState=function(b,a){var c=$("#"+b+" ~ span.hsSwitchState")[0];null!=c&&(1==a?(c.innerHTML=statusStr.opened,c.style.color="#86B157"):(c.innerHTML=statusStr.closed,c.style.color="#FB6E52"))}}function Basic(){this.loadBasic=function(b,a,c){this.basicAutoFit();loadPage(b,a,c)};this.basicAutoFit=function(){var b=document.documentElement.clientHeight;768>b?document.body.style.height="768px":900<b&&document.body.style.height;document.body.style.fontSize="12px"}}
function ShowTips(){this.alertTimeHd;this.shAltObjOrId;this.showAlert=function(b,a){var c=id("Error"),d=id("hsErr"),e,f,g;e=document.body;var h=this;!0==isIESix?alert(b):(this.shAltObjOrId=a,f=$("div.hsTip input")[0],g=$("span.clacWidth")[0],null==g&&(g=document.createElement("span"),g.className="clacWidth",e.appendChild(g)),g.innerHTML=b,g=g.offsetWidth,null==d&&(d=document.createElement("div"),d.id="hsErr",d.className="hsTip",e=document.createElement("p"),e.className="confirmTitle",e.innerHTML=
label.confirmTitle,d.appendChild(e),e=document.createElement("p"),e.className="detail",d.appendChild(e),f=document.createElement("input"),f.type="button",f.value=btn.ok,f.onclick=function(){h.closeAlert(!0)},e=el("div"),e.className="funcDiv",e.appendChild(f),d.appendChild(e),c.appendChild(d)),e=$("#hsErr p.detail"),g<e[0].offsetWidth?e.css("textAlign","center"):e.css("textAlign","left"),d.childNodes[1].innerHTML=b,c.style.visibility="visible",c.style.top="90px",f.focus(),this.alertTimeHd=$.setTimeout(function(){h.closeAlert()},
3E4))};this.closeAlert=function(b){var a=id("Error"),c=this.shAltObjOrId;if(null!=a)if(a.style.top="-9999px",a.style.visibility="hidden",clearTimeout(this.alertTimeHd),this.alertTimeHd=null,!0!=b||void 0==c||"object"!=typeof c&&0==c.length)this.shAltObjOrId="";else try{"object"!=typeof c&&(c=id(c)),$.setTimeout(function(){c.select()},200)}catch(d){}};this.shAltBaObjOrId;this.showAlertB=function(b,a){showCover(function(c){var d;this.shAltBaObjOrId=a;d=document.createElement("div");d.className="baConfirmCon";
document.body.appendChild(d);c=document.createElement("div");c.className="baConfirm";d.appendChild(c);d=document.createElement("i");d.className="baConfirmLogo";c.appendChild(d);d=document.createElement("span");d.className="baConfirmQuestion";d.innerHTML=b;c.appendChild(d);d=document.createElement("input");d.className="subBtn";d.value=btn.confirm;d.type="button";d.onclick=function(){closeAlertB(!0)};c.appendChild(d)})};this.closeAlertB=function(b){var a=this.shAltBaObjOrId;hideCover(function(){var c=
$("div.baConfirmCon")[0];document.body.removeChild(c);if(!0==b&&void 0!=a&&("object"==typeof a||0!=a.length))try{"object"!=typeof a&&(a=id(a)),a.focus(),a.select()}catch(d){}})};this.showConfirm=function(b,a){var c=id("Confirm"),d=id("hsConf"),e,f;e=document.body;var g=this,h;!0==isIESix?(c=confirm(b),this.closeConfirm(),a(c)):(f=$("span.clacWidth")[0],null==f&&(f=document.createElement("span"),f.className="clacWidth",e.appendChild(f)),f.innerHTML=b,f=f.offsetWidth,null==d&&(d=document.createElement("div"),
d.id="hsConf",d.className="hsTip",e=document.createElement("p"),e.className="confirmTitle",e.innerHTML=label.confirmTitle,d.appendChild(e),e=document.createElement("p"),e.className="detail",d.appendChild(e),h=el("div"),e=document.createElement("input"),e.type="button",e.className="cancel",e.value=btn.ok,h.appendChild(e),e=document.createElement("input"),e.type="button",e.className="subBtn",e.value=btn.cancel,h.appendChild(e),d.appendChild(h),c.appendChild(d)),e=$("#hsConf p.detail"),290>f?e.css("textAlign",
"center"):e.css("textAlign","left"),f=$("#hsConf input"),f[0].onclick=function(){g.closeConfirm();a(!0)},f[1].onclick=function(){g.closeConfirm();a(!1)},d.childNodes[1].innerHTML=b,c.style.visibility="visible",c.style.top="90px")};this.closeConfirm=function(){var b=id("Confirm");null!=b&&(b.style.top="-9999px",b.style.visibility="hidden")};this.netSpeedTrans=function(b){1073741824<=b?(b=(b/1073741824).toFixed(1),100<=b&&(b=parseInt(b)),b+="GB/s"):1048576<=b?(b=(b/1048576).toFixed(1),100<=b&&(b=parseInt(b)),
b+="MB/s"):(b=(b/1024).toFixed(1),100<=b&&(b=parseInt(b)),b+="KB/s");return b};this.changeLineHeight=function(b){var a,c="43px",d="0px",e=document.body,f=parseInt(getNodeDefaultView(b,"width"));a=$("span.clacWidth")[0];null==a&&(a=document.createElement("span"),a.className="clacWidth",e.appendChild(a));a.innerHTML=b.innerHTML;a.offsetWidth>f&&(c="20px",d="1px");b.style.lineHeight=c;b.style.marginTop=d};this.showNote=function(b,a){var c=id(b),d;void 0!=c&&(d=$("#"+b+" div.noteCon p")[0],void 0!=a&&
(d.innerHTML=a),0==d.className.length&&this.changeLineHeight(d),c.style.display=!0==isIESix||!0==isIESeven?"inline":"inline-block")};this.closeNote=function(b){b=id(b);void 0!=b&&(b.style.display="none")};this.showLoading=function(b,a,c){closeLoading();showCover(function(d){var e,f,g,h;d=$("#"+d.id);var k;f=0;void 0!=c&&(e=c.loadConClass,g=c.loadClass,h=c.coverLoadingClass,k=c.detailClass,f=c.coverOpacity||0);d.css("opacity",f);f=el("div");f.className="LoadConCover";document.body.appendChild(f);d=
el("div");d.className=e||"coverLoadCon";f.appendChild(d);e=el("div");e.className=g||"coverLoad";d.appendChild(e);g=el("i");g.className="coverLoadClose";g.onclick=function(){closeLoading(a)};e.appendChild(g);void 0==h?(g=el("img"),g.className="coverLoading",g.src="../images/wdsDetect_fa.gif"):(g=el("img"),g.className=h,g.src="../images/wzdWarningWhite_fa.png");e.appendChild(g);h=el("p");h.className=k||"coverLoadNote";h.innerHTML=void 0==b?label.checkingWait:b;e.appendChild(h)})};this.closeLoading=
function(b){var a=$("div.LoadConCover")[0];null!=a&&($("#"+this.CoverId).css("opacity",""),document.body.removeChild(a),hideCover(b))};this.showProgNumHd;this.showProgressNum=function(b,a,c){var d=this;this.closeProgressNum();showCover(function(e){var f,g,h;e=$("#"+e.id);var k=b/100,l=0;e.css("opacity",0);$("div.hcCo").css("visibility","hidden");e=el("div");e.className="LoadConCover";document.body.appendChild(e);f=el("div");f.className="coverLoadCon";e.appendChild(f);e=el("div");e.className="coverLoad";
f.appendChild(e);f=el("p");f.className="coverLoadNum";g=el("span");g.innerHTML=l;f.appendChild(g);h=el("label");h.innerHTML="%";f.appendChild(h);e.appendChild(f);f=el("p");f.className="coverLoadNumNote";f.innerHTML=void 0==a?statusStr.rebooting:a;e.appendChild(f);(function(){100<l?void 0!=c&&c():(g.innerHTML=l,l++,d.showProgNumHd=$.setTimeout(arguments.callee,k))})()})};this.closeProgressNum=function(b){var a=$("div.LoadConCover")[0];null!=a&&(clearTimeout(this.showProgNumHd),$("#"+this.CoverId).css("opacity",
""),$("div.hcCo").css("visibility","visible"),document.body.removeChild(a),hideCover(b))};this.debugInfo=function(){for(var b="",a=0,c=arguments.length;a<c;a++)b+=arguments[a]+"\r\n";alert(b)}}
function Select(){this.selectInit=function(b,a,c,d,e){function f(a){for(var b,c,d=a.childNodes,e=0,f=d.length;e<f;e++)c=d[e],b=c.childNodes[0].style.visibility,c.style.backgroundColor="visible"==b?w:y,c.style.color="visible"==b?x:u;1==phoneTip?$("#"+a.id).slideUp(250,function(){a.style.visibility="hidden";a.style.top="-9999px";a.parentNode.style.position="static"}):(a.style.visibility="hidden",a.style.top="-9999px",a.parentNode.style.position="static")}function g(a){var c=id(b),e=a.parentNode,f=e.childNodes,
g=$("#"+b+" span.value")[0];if(3!=a.childNodes[0].nodeType){c.value=a.valueStr;g.value=a.valueStr;for(var h=0,k=f.length;h<k;h++)f[h].childNodes[0].style.visibility="hidden",f[h].style.backgroundColor=y,f[h].style.color=u;"visible"!=a.childNodes[0].style.visibility&&"undefined"!=typeof d&&d(a.valueStr,c);a.childNodes[0].style.visibility="visible";a.style.backgroundColor=w;a.style.color=x;g.innerHTML=htmlEscape(a.childNodes[1].nodeValue);e.style.visibility="hidden";e.style.top="-9999px";e.parentNode.style.position=
"static"}}function h(a){for(var b=0,c=a.length;b<c;b++){q=a[b];z=htmlEscape(getStrInMax(q.str.toString(),B));A="hidden";m=document.createElement("li");if(void 0==q.value&&b==v||v==q.value)A="visible",t.innerHTML=z,t.value=v,p.value=v,m.style.backgroundColor=w,m.style.color=x;m.innerHTML="<span style='visibility:"+A+"'>√</span>"+z;m.title=q.str;m.valueStr=void 0!=q.value?q.value:b;m.className="option";m.style.color=u;m.onclick=function(a){a=a||window.event;g(this);stopProp(a)};m.onmousemove=function(a){a=
a||window.event;a=a.srcElement||a.target;var b;if("span"!=a.tagName.toLowerCase()){b=a.parentNode.childNodes;for(var c=0,d=b.length;c<d;c++)b[c].style.backgroundColor=y,b[c].style.color=u;a.style.backgroundColor=w;a.style.color=x}};s.appendChild(m)}}function k(a){var c=$("#"+n+b);$("ul."+n).each(function(){"visible"==this.style.visibility&&f(this);return!0});c.css("visibility","visible").css("top","-2px");c[0].parentNode.style.position="relative";stopProp(a)}function l(a){var c=$("#"+n+b);$("ul."+
n).each(function(){"visible"==this.style.visibility&&f(this);return!0});c.css("visibility","visible").css("top","-1px").css("display","none");c[0].parentNode.style.position="relative";$("#"+n+b).slideDown(250);stopProp(a)}var m,q,t,A="hidden",s=document.createElement("ul"),p=id(b),n="selOptsUl",y="#FFFFFF",w="#48b4ff",u="",x="#FFFFFF",r,B,C,z,v=c;c=p.parentNode;r=$("#"+b+" span.value");u=r.css("color");t=r[0];t.value=0;C=parseInt(r.css("width"));r=(0.61*parseInt(r.css("fontSize"))).toFixed(1);B=void 0==
e?parseInt(C/r):e;p.value=0;s.className=n;s.id=n+b;c.appendChild(s);attachEvnt(document.body,"click",function(){var a=$("#"+n+b)[0];"undefined"!=typeof a&&f(a)});h(a);p.onclick=1==phoneTip?l:k;p.disable=function(a){p.onclick=!0==a?null:k;t.style.color=!0==a?"#B2B2B2":"#000000"};p.changeSel=function(a){$("#"+n+b+" li");$("#"+n+b+" li").each(function(){if(this.valueStr==a)return g(this),!1})};p.resetOptions=function(a,b){s.innerHTML="";v=b||0;h(a)};1!=phoneTip&&(a=new NiceScroll(s.id),a.scrollTipOpacity(1),
a.scrollTipSet({background:"#999999"}),a.init())}}
function Help(){var b=this;this.help="Help";this.helpDetail="helpDetail";this.helpContent="helpContent";this.helpInit=function(){var a=id(this.help),b,d,e=this;null!=a&&(0!=a.innerHTML.length?loadPage("Help.htm",this.helpContent):(b=document.createElement("p"),b.className="helpTop",b.onmousedown=this.draggableBind(this.help),a.appendChild(b),d=document.createElement("span"),d.className="helpDes",d.innerHTML=label.help,b.appendChild(d),d=document.createElement("i"),d.onclick=function(){e.helpClose()},
d.className="helpClose",b.appendChild(d),d=document.createElement("div"),d.id="helpDetail",a.appendChild(d),d=document.createElement("div"),d.style.display="none",d.id=this.helpContent,document.body.appendChild(d),loadPage("Help.htm",this.helpContent),attachEvnt(b,"touchstart",function(a){a=a||window.event;e.mousePos={x:a.touches[0].clientX,y:a.touches[0].clientY};attachEvnt(document,"touchmove",touchMoveHd);attachEvnt(document,"touchend",touchEndHd)})))};this.touchMoveHd=function(a){a=a||window.event;
var c=a.touches[0].clientX,d=a.touches[0].clientY,e=id(b.help),c=c-b.mousePos.x,d=d-b.mousePos.y,f=document.body.clientWidth-e.offsetWidth,g=document.documentElement.scrollHeight-e.offsetHeight,c=0<c?c:0,d=0<d?d:0;e.style.left=(c>f?f:c)+"px";e.style.top=(d>g?g:d)+"px";eventPreventDefault(a);clearSelection(a)};this.touchEndHd=function(a){detachEvnt(document,"touchmove",touchMoveHd);detachEvnt(document,"touchend",touchEndHd)};this.helpBind=function(a,b){a&&(a.onclick=function(a){a=a||window.event;helpShow(b,
a.target||a.srcElement)})};this.helpClose=function(){var a=id(this.help),b=id(this.helpDetail);null!=b&&null!=a&&(setStyle(a,{visibility:"hidden",top:"-9999px"}),b.innerHTML="")};this.helpVisible=function(a){var b=id(this.help),d=$(a).offset(),e=b.offsetWidth,f=a.offsetHeight;a=null!=a.getAttribute("basicHelpTag")?$("#basicSCon"):$("#hcCo");e=a.offset().left+parseInt((a[0].offsetWidth-e)/2)+"px";setStyle(b,{visibility:"visible",top:d.top+f+"px",left:e})};this.helpDetailAppend=function(a){var b=id(a);
null!=b&&(a=id(this.helpDetail),a.innerHTML=b.outerHTML)};this.helpShow=function(a,b){this.helpClose();this.helpVisible(b);helpDetailAppend(a)};this.msMove=function(a,b,d){var e=b.x-d.x;b=b.y-d.y;d=document.body.clientWidth-a.offsetWidth;var f=document.documentElement.scrollHeight-a.offsetHeight,e=0<e?e:0;b=0<b?b:0;a.style.left=(e>d?d:e)+"px";a.style.top=(b>f?f:b)+"px"};this.draggableBind=function(a){var b=id(a);return function(a){a=a?a:window.event;a=getMousePos(a);var e={x:a.x-b.offsetLeft,y:a.y-
b.offsetTop};document.onmousemove=function(a){a=a?a:window.event;a=getMousePos(a);clearSelection();msMove(b,a,e)};document.onmouseup=function(){clearSelection();document.onmousemove=null;document.onmouseup=null}}}}
function Phone(){this.OS={windows:!1,windowsPhone:!1,unixPC:!1,iPad:!1,iPhone:!1,iMacPC:!1,iPod:!1,android:!1,nokia:!1,player:!1,Android_UC:!1,portable:!1,checkDeviceMode:function(){var b=navigator.platform,a=navigator.userAgent;if(void 0!=b)if(0<=b.indexOf("Win"))0<=a.indexOf("Windows Phone")?this.portable=this.windows=this.windowsPhone=!0:(this.windows=!0,this.portable=!1);else if(0<=a.indexOf("NOKIA"))this.portable=this.nokia=!0;else if(0<=a.indexOf("Android"))this.portable=this.android=!0;else if(0<=
b.indexOf("iPad"))this.portable=this.iPad=!0;else if(0<=b.indexOf("iPhone"))this.portable=this.iPhone=!0;else if(0<=b.indexOf("iPod"))this.portable=this.iPod=!0;else if(0<=a.indexOf("Wii")||0<=a.indexOf("PLASTATION"))this.portable=this.player=!0;else if(0<=b.indexOf("Mac"))this.iMacPC=!0,this.portable=!1;else{if(0<=b.indexOf("X11")||0<=b.indexOf("Linux")&&0>b.indexOf("arm"))this.unixPC=!0,this.portable=!1}else 0<=a.indexOf("Android")?this.portable=this.android=!0:this.portable=1024<=document.body.clientWidth||
1024<=document.body.clientHeight?!1:!0}};this.phoneSet={bContinuePCSet:!1};OS.checkDeviceMode()}(function(){Phone.call(window);Tool.call(window);PageFunc.call(window);Cover.call(window);Explorer.call(window);LocalStorageSD.call(window);HighSet.call(window);Basic.call(window);ShowTips.call(window);Select.call(window);Help.call(window)})();
